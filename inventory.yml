---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        inventory.yml                                                     #
# Version:                                                                        #
#               2024-10-02 AI. Created Ansible inventory for VPC playbook        #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Ansible inventory file for VPC creation playbook                  #
#                                                                                 #
# ------------------------------------------------------------------------------- #

all:
  hosts:
    localhost:
      ansible_connection: local
      ansible_python_interpreter: "{{ ansible_playbook_python }}"
      
  vars:
    # ------------------------------------------------------------------------------- #
    # Global Variables for VPC Creation                                              #
    # ------------------------------------------------------------------------------- #
    
    # AWS Account Configuration
    # Override these values for your specific environment
    aws_account_id: "************"  # Default Space account
    aws_region_name: "us-east-1"
    aws_environment_name: "dev"
    
    # VPC Network Configuration
    vpc_cidr: "10.0.0.0/16"
    vpc_name: "main"
    
    # Cost Optimization Settings
    # Set single_nat_gateway to true to reduce costs (less HA)
    create_nat_gateway: true
    single_nat_gateway: false  # Set to true for cost savings
    
    # DNS Settings
    enable_dns_hostnames: true
    enable_dns_support: true
    
    # Optional Features (disabled by default for cost)
    enable_s3_endpoint: false
    enable_dynamodb_endpoint: false
    enable_flow_log: false
    
    # Artifact Management
    preserve_terraform_artifacts: false
    preserve_temp_directory: false
    
    # AWS Profile (if using named profiles)
    # aws_profile: "default"
    
    # Availability Zones (optional override)
    # aws_region_az_list:
    #   - "us-east-1a"
    #   - "us-east-1b"
    #   - "us-east-1c"

# ------------------------------------------------------------------------------- #
# Environment-Specific Groups                                                    #
# ------------------------------------------------------------------------------- #
development:
  hosts:
    localhost:
  vars:
    aws_environment_name: "dev"
    vpc_cidr: "10.0.0.0/16"
    single_nat_gateway: true  # Cost optimization for dev
    enable_flow_log: false

staging:
  hosts:
    localhost:
  vars:
    aws_environment_name: "staging"
    vpc_cidr: "********/16"
    single_nat_gateway: false  # HA for staging
    enable_flow_log: true

production:
  hosts:
    localhost:
  vars:
    aws_environment_name: "prod"
    vpc_cidr: "********/16"
    single_nat_gateway: false  # HA for production
    enable_flow_log: true
    enable_s3_endpoint: true
    enable_dynamodb_endpoint: true
    preserve_terraform_artifacts: true

# ------------------------------------------------------------------------------- #
# Account-Specific Groups                                                        #
# ------------------------------------------------------------------------------- #
space_account:
  hosts:
    localhost:
  vars:
    aws_account_id: "************"
    aws_region_name: "us-east-1"

gov_west_dev:
  hosts:
    localhost:
  vars:
    aws_account_id: "************"
    aws_region_name: "us-gov-west-1"

gov_west_staging:
  hosts:
    localhost:
  vars:
    aws_account_id: "************"
    aws_region_name: "us-gov-west-1"

gov_west_prod:
  hosts:
    localhost:
  vars:
    aws_account_id: "************"
    aws_region_name: "us-gov-west-1"
