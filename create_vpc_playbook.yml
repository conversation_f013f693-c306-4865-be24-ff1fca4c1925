---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_vpc_playbook.yml                                           #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC creation playbook                      #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Ansible playbook to create AWS VPC infrastructure using           #
#               Terraform modules and the aws_tfvpc role                          #
#                                                                                 #
# Usage:                                                                          #
#   ansible-playbook create_vpc_playbook.yml                                      #
#   ansible-playbook create_vpc_playbook.yml -e vpc_cidr="********/16"           #
#   ansible-playbook create_vpc_playbook.yml -e environment=staging               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Create AWS VPC Infrastructure
  hosts: localhost
  gather_facts: true
  connection: local
  
  vars:
    # ------------------------------------------------------------------------------- #
    # Default VPC Configuration                                                      #
    # You can override these variables using -e flag or in inventory                 #
    # ------------------------------------------------------------------------------- #
    
    # Environment Configuration
    aws_account_id: "{{ aws_account_id | default('************') }}"  # Default to Space account
    aws_region_name: "{{ aws_region_name | default('us-east-1') }}"
    aws_environment_name: "{{ aws_environment_name | default('dev') }}"
    
    # VPC Configuration
    vpc_cidr: "{{ vpc_cidr | default('10.0.0.0/16') }}"
    vpc_name: "{{ vpc_name | default('main') }}"
    
    # NAT Gateway Configuration
    create_nat_gateway: "{{ create_nat_gateway | default(true) }}"
    single_nat_gateway: "{{ single_nat_gateway | default(false) }}"  # Set to true for cost optimization
    
    # DNS Configuration
    enable_dns_hostnames: "{{ enable_dns_hostnames | default(true) }}"
    enable_dns_support: "{{ enable_dns_support | default(true) }}"
    
    # VPC Endpoints (optional)
    enable_s3_endpoint: "{{ enable_s3_endpoint | default(false) }}"
    enable_dynamodb_endpoint: "{{ enable_dynamodb_endpoint | default(false) }}"
    
    # Flow Logs (optional)
    enable_flow_log: "{{ enable_flow_log | default(false) }}"
    
    # Artifact preservation
    preserve_terraform_artifacts: "{{ preserve_terraform_artifacts | default(false) }}"
    preserve_temp_directory: "{{ preserve_temp_directory | default(false) }}"
    
  tasks:
    # ------------------------------------------------------------------------------- #
    # Pre-flight Checks                                                              #
    # ------------------------------------------------------------------------------- #
    - name: Display playbook information
      ansible.builtin.debug:
        msg: |
          AWS VPC Creation Playbook
          ========================
          Account: {{ aws_account_id }}
          Region: {{ aws_region_name }}
          Environment: {{ aws_environment_name }}
          VPC CIDR: {{ vpc_cidr }}
          NAT Gateway: {{ create_nat_gateway }}
          Single NAT: {{ single_nat_gateway }}
          
          This playbook will create:
          - VPC with public, private, and database subnets
          - Internet Gateway for public subnet access
          {% if create_nat_gateway %}
          - NAT Gateway(s) for private subnet internet access
          {% endif %}
          - Route tables and associations
          - Proper security group configurations

    - name: Validate required variables
      ansible.builtin.assert:
        that:
          - aws_account_id is defined
          - aws_account_id | length > 0
          - aws_region_name is defined
          - aws_region_name | length > 0
          - aws_environment_name is defined
          - aws_environment_name | length > 0
          - vpc_cidr is defined
          - vpc_cidr | length > 0
        fail_msg: "Required variables are missing. Please check aws_account_id, aws_region_name, aws_environment_name, and vpc_cidr."
        success_msg: "All required variables are present."

    # ------------------------------------------------------------------------------- #
    # Set up AWS Configuration                                                        #
    # ------------------------------------------------------------------------------- #
    - name: Set AWS account configuration
      ansible.builtin.set_fact:
        aws_account:
          id: "{{ aws_account_id }}"
          
    - name: Set AWS region configuration
      ansible.builtin.set_fact:
        aws_region:
          name: "{{ aws_region_name }}"
          id: "{{ aws_region_name | regex_replace('-', '') | regex_replace('us', 'u') | regex_replace('gov', 'g') | regex_replace('east', 'e') | regex_replace('west', 'w') | regex_replace('1', '1') }}"
          partition: "{{ 'aws-us-gov' if 'gov' in aws_region_name else 'aws' }}"
          az_list: "{{ aws_region_az_list | default([aws_region_name + 'a', aws_region_name + 'b', aws_region_name + 'c']) }}"

    - name: Set AWS environment configuration
      ansible.builtin.set_fact:
        aws_environment:
          id: "{{ aws_environment_name }}"

    # ------------------------------------------------------------------------------- #
    # Set up Common AWS Configuration                                                 #
    # ------------------------------------------------------------------------------- #
    - name: Set AWS common configuration
      ansible.builtin.set_fact:
        aws_common:
          service_name: "AWS VPC Infrastructure"
          service_id: "vpc"
          tags:
            Project: "VPC-{{ aws_environment_name }}"
            Owner: "{{ ansible_user | default('ansible') }}"
            Purpose: "VPC Infrastructure for {{ aws_environment_name }}"
            CreatedBy: "ansible-vpc-playbook"
            
    - name: Set AWS credentials configuration
      ansible.builtin.set_fact:
        aws_common_credentials:
          init:
            profile: "{{ aws_profile | default('default') }}"
            access_key_id: "{{ lookup('env', 'AWS_ACCESS_KEY_ID') | default('') }}"
            access_key_secret: "{{ lookup('env', 'AWS_SECRET_ACCESS_KEY') | default('') }}"

    # ------------------------------------------------------------------------------- #
    # Configure VPC Parameters                                                       #
    # ------------------------------------------------------------------------------- #
    - name: Set VPC configuration
      ansible.builtin.set_fact:
        aws_tfvpc_config:
          vpc_cidr: "{{ vpc_cidr }}"
          enable_dns_hostnames: "{{ enable_dns_hostnames }}"
          enable_dns_support: "{{ enable_dns_support }}"
          create_igw: true
          create_nat_gateway: "{{ create_nat_gateway }}"
          single_nat_gateway: "{{ single_nat_gateway }}"
          one_nat_gateway_per_az: "{{ not single_nat_gateway }}"
          map_public_ip_on_launch: true
          enable_flow_log: "{{ enable_flow_log }}"
          enable_s3_endpoint: "{{ enable_s3_endpoint }}"
          enable_dynamodb_endpoint: "{{ enable_dynamodb_endpoint }}"
          
          # Additional tags for VPC resources
          vpc_tags:
            Name: "{{ aws_region.id }}-{{ aws_environment.id }}-vpc"
            VpcName: "{{ vpc_name }}"
            
          public_subnet_tags:
            Type: "public"
            kubernetes.io/role/elb: "1"
            
          private_subnet_tags:
            Type: "private"
            kubernetes.io/role/internal-elb: "1"
            
          database_subnet_tags:
            Type: "database"

    # ------------------------------------------------------------------------------- #
    # Set Artifact Preservation Options                                              #
    # ------------------------------------------------------------------------------- #
    - name: Set artifact preservation options
      ansible.builtin.set_fact:
        aws_tfvpc_preserve_artifacts: "{{ preserve_terraform_artifacts }}"
        aws_tfvpc_preserve_tempdir: "{{ preserve_temp_directory }}"
        aws_tfvpc_artifacts_dir: "./vpc_artifacts_{{ aws_account_id }}_{{ aws_region_name }}_{{ aws_environment_name }}"

    # ------------------------------------------------------------------------------- #
    # Execute VPC Creation                                                            #
    # ------------------------------------------------------------------------------- #
    - name: Create VPC infrastructure using aws_tfvpc role
      ansible.builtin.include_role:
        name: aws_tfvpc
      vars:
        # Pass all the configuration to the role
        aws_tfvpc_config: "{{ aws_tfvpc_config }}"
        aws_common_credentials: "{{ aws_common_credentials }}"
        aws_account: "{{ aws_account }}"
        aws_region: "{{ aws_region }}"
        aws_environment: "{{ aws_environment }}"
        aws_common: "{{ aws_common }}"

    # ------------------------------------------------------------------------------- #
    # Display Results                                                                 #
    # ------------------------------------------------------------------------------- #
    - name: Display VPC creation results
      ansible.builtin.debug:
        msg: |
          VPC Infrastructure Creation Complete!
          ====================================
          
          VPC Details:
          - VPC ID: {{ vpc_outputs.vpc_id | default('Not available') }}
          - VPC CIDR: {{ vpc_outputs.vpc_cidr_block | default('Not available') }}
          - Region: {{ aws_region_name }}
          - Environment: {{ aws_environment_name }}
          
          Subnets Created:
          - Public Subnets: {{ vpc_outputs.public_subnets | default([]) | length }}
          - Private Subnets: {{ vpc_outputs.private_subnets | default([]) | length }}
          - Database Subnets: {{ vpc_outputs.database_subnets | default([]) | length }}
          
          Network Components:
          - Internet Gateway: {{ vpc_outputs.igw_id | default('Not created') }}
          - NAT Gateways: {{ vpc_outputs.nat_ids | default([]) | length }}
          
          {% if preserve_terraform_artifacts %}
          Artifacts preserved in: {{ aws_tfvpc_artifacts_dir }}
          {% endif %}
          
          Next Steps:
          1. Verify the VPC in AWS Console
          2. Update security groups as needed
          3. Deploy applications to appropriate subnets
          4. Configure additional VPC endpoints if required

    # ------------------------------------------------------------------------------- #
    # Save VPC Information                                                            #
    # ------------------------------------------------------------------------------- #
    - name: Save VPC information to file
      ansible.builtin.copy:
        content: |
          # VPC Information for {{ aws_environment_name }} environment
          # Generated on {{ ansible_date_time.iso8601 }}
          
          VPC_ID={{ vpc_outputs.vpc_id | default('') }}
          VPC_CIDR={{ vpc_outputs.vpc_cidr_block | default('') }}
          
          # Public Subnets
          {% for subnet in vpc_outputs.public_subnets | default([]) %}
          PUBLIC_SUBNET_{{ loop.index }}={{ subnet }}
          {% endfor %}
          
          # Private Subnets  
          {% for subnet in vpc_outputs.private_subnets | default([]) %}
          PRIVATE_SUBNET_{{ loop.index }}={{ subnet }}
          {% endfor %}
          
          # Database Subnets
          {% for subnet in vpc_outputs.database_subnets | default([]) %}
          DATABASE_SUBNET_{{ loop.index }}={{ subnet }}
          {% endfor %}
          
          # Network Components
          IGW_ID={{ vpc_outputs.igw_id | default('') }}
          {% for nat in vpc_outputs.nat_ids | default([]) %}
          NAT_GATEWAY_{{ loop.index }}={{ nat }}
          {% endfor %}
          
        dest: "./vpc_info_{{ aws_account_id }}_{{ aws_region_name }}_{{ aws_environment_name }}.env"
        mode: '0644'
      when: vpc_outputs.vpc_id is defined

  # ------------------------------------------------------------------------------- #
  # Error Handling                                                                  #
  # ------------------------------------------------------------------------------- #
  rescue:
    - name: Display error information
      ansible.builtin.debug:
        msg: |
          VPC Creation Failed!
          ===================
          
          An error occurred during VPC creation. Please check:
          1. AWS credentials are valid and have sufficient permissions
          2. The specified AWS account and region are correct
          3. The VPC CIDR doesn't conflict with existing VPCs
          4. Terraform is installed and accessible
          5. Required Ansible collections are installed
          
          Error details should be visible in the output above.
          
          For troubleshooting:
          - Check AWS CloudTrail for API call failures
          - Verify IAM permissions for VPC, EC2, and related services
          - Ensure the aws_tfvpc role is properly installed
          
    - name: Fail the playbook with error message
      ansible.builtin.fail:
        msg: "VPC creation failed. Please check the error messages above and retry."
