# AWS VPC Creation with Ansible and Terraform

This repository provides a complete solution for creating AWS VPC infrastructure using Ansible to orchestrate Terraform modules. The solution is designed for beginners and follows enterprise best practices.

## 🏗️ What This Creates

The Ansible playbook will create a complete VPC infrastructure including:

- **VPC** with configurable CIDR block
- **Public Subnets** (with Internet Gateway access)
- **Private Subnets** (with NAT Gateway access)
- **Database Subnets** (isolated, no direct internet access)
- **Internet Gateway** for public subnet internet access
- **NAT Gateway(s)** for private subnet outbound internet access
- **Route Tables** properly configured for each subnet type
- **Security Groups** with default configurations
- **Proper tagging** for cost tracking and organization

## 📋 Prerequisites

### Required Software
- **Ansible** >= 2.16.11
- **Terraform** >= 1.0
- **AWS CLI** (configured with credentials)
- **Python** >= 3.8

### Required Ansible Collections
```bash
ansible-galaxy collection install community.general
ansible-galaxy collection install amazon.aws
```

### AWS Permissions
Your AWS credentials need permissions for:
- VPC management (create, modify, delete VPCs, subnets, route tables)
- EC2 management (Internet Gateways, NAT Gateways, Elastic IPs)
- IAM (for resource tagging and policies)
- S3 and DynamoDB (for Terraform state management)

## 🚀 Quick Start

### 1. Set Up AWS Credentials

```bash
# Option 1: Environment variables
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"

# Option 2: AWS CLI profile
aws configure --profile myprofile
```

### 2. Basic VPC Creation

```bash
# Create a basic VPC with default settings
ansible-playbook -i inventory.yml create_vpc_playbook.yml

# Create VPC with custom CIDR
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  -e vpc_cidr="********/16"

# Create VPC for specific environment
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  -e aws_environment_name="staging" \
  -e vpc_cidr="********/16"
```

### 3. Environment-Specific Deployment

```bash
# Development environment (cost-optimized)
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  --limit development

# Staging environment (high availability)
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  --limit staging

# Production environment (full features)
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  --limit production
```

## ⚙️ Configuration Options

### Basic Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `vpc_cidr` | `10.0.0.0/16` | CIDR block for the VPC |
| `aws_account_id` | `************` | AWS Account ID |
| `aws_region_name` | `us-east-1` | AWS Region |
| `aws_environment_name` | `dev` | Environment name |

### Network Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `create_nat_gateway` | `true` | Create NAT gateways for private subnets |
| `single_nat_gateway` | `false` | Use single NAT gateway (cost optimization) |
| `enable_dns_hostnames` | `true` | Enable DNS hostnames in VPC |
| `enable_dns_support` | `true` | Enable DNS support in VPC |

### Optional Features

| Variable | Default | Description |
|----------|---------|-------------|
| `enable_s3_endpoint` | `false` | Create S3 VPC endpoint |
| `enable_dynamodb_endpoint` | `false` | Create DynamoDB VPC endpoint |
| `enable_flow_log` | `false` | Enable VPC Flow Logs |

### Cost Optimization

```bash
# Minimal cost VPC (single NAT gateway)
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  -e single_nat_gateway=true \
  -e enable_flow_log=false

# No NAT gateway (private subnets without internet)
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  -e create_nat_gateway=false
```

## 📁 Project Structure

```
.
├── create_vpc_playbook.yml          # Main Ansible playbook
├── inventory.yml                    # Ansible inventory with examples
├── roles/
│   └── aws_tfvpc/                   # VPC creation role
│       ├── tasks/
│       │   ├── main.yml             # Main role entry point
│       │   ├── aws_tfvpc_build.yml  # Build Terraform files
│       │   ├── aws_tfvpc_apply.yml  # Apply Terraform
│       │   ├── aws_tfvpc_outputs.yml # Get outputs
│       │   └── aws_tfvpc_cleanup.yml # Cleanup
│       ├── templates/               # Jinja2 templates for Terraform
│       │   ├── vpc_main.tf.j2       # Main Terraform config
│       │   ├── vpc_providers.tf.j2  # Provider config
│       │   ├── vpc_outputs.tf.j2    # Outputs config
│       │   ├── vpc_variables.tf.j2  # Variables config
│       │   ├── vpc_terraform.tf.j2  # Backend config
│       │   └── vpc_credentials.j2   # AWS credentials
│       └── meta/
│           ├── main.yml             # Role metadata
│           └── argument_specs.yml   # Parameter validation
└── terraform/
    └── modules/
        └── vpc/                     # Terraform VPC module
            ├── main.tf              # VPC resources
            ├── variables.tf         # Input variables
            ├── outputs.tf           # Output values
            ├── versions.tf          # Version constraints
            └── README.md            # Module documentation
```

## 🔧 Advanced Usage

### Custom Account and Region

```bash
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  -e aws_account_id="************" \
  -e aws_region_name="us-west-2" \
  -e aws_environment_name="prod" \
  -e vpc_cidr="10.2.0.0/16"
```

### With VPC Endpoints (for private subnet access to AWS services)

```bash
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  -e enable_s3_endpoint=true \
  -e enable_dynamodb_endpoint=true \
  -e enable_flow_log=true
```

### Preserve Terraform Artifacts

```bash
ansible-playbook -i inventory.yml create_vpc_playbook.yml \
  -e preserve_terraform_artifacts=true \
  -e preserve_temp_directory=true
```

## 📊 Understanding the Output

After successful execution, you'll see:

```
VPC Infrastructure Creation Complete!
====================================

VPC Details:
- VPC ID: vpc-0123456789abcdef0
- VPC CIDR: 10.0.0.0/16
- Region: us-east-1
- Environment: dev

Subnets Created:
- Public Subnets: 3
- Private Subnets: 3
- Database Subnets: 3

Network Components:
- Internet Gateway: igw-0123456789abcdef0
- NAT Gateways: 3
```

The playbook also creates a file `vpc_info_<account>_<region>_<environment>.env` with all the resource IDs for use in other scripts.

## 🛠️ Troubleshooting

### Common Issues

1. **Permission Denied**
   ```
   Error: Access Denied
   ```
   - Check AWS credentials
   - Verify IAM permissions for VPC operations

2. **CIDR Conflicts**
   ```
   Error: CIDR block conflicts with existing VPC
   ```
   - Choose a different CIDR block
   - Check existing VPCs in the region

3. **Terraform Not Found**
   ```
   Error: terraform command not found
   ```
   - Install Terraform: https://terraform.io/downloads

4. **Ansible Collection Missing**
   ```
   Error: community.general collection not found
   ```
   - Install required collections:
     ```bash
     ansible-galaxy collection install community.general
     ```

### Debug Mode

```bash
# Run with verbose output
ansible-playbook -i inventory.yml create_vpc_playbook.yml -vvv

# Check what would be created (dry run)
ansible-playbook -i inventory.yml create_vpc_playbook.yml --check
```

## 🧹 Cleanup

To destroy the VPC infrastructure:

```bash
# Navigate to the preserved artifacts directory
cd vpc_artifacts_<account>_<region>_<environment>

# Destroy infrastructure
terraform destroy -auto-approve
```

## 📚 Next Steps

After creating your VPC:

1. **Security Groups**: Create application-specific security groups
2. **EKS Cluster**: Deploy Kubernetes cluster in the private subnets
3. **RDS Database**: Deploy databases in the database subnets
4. **Load Balancers**: Deploy ALB/NLB in public subnets
5. **Monitoring**: Set up CloudWatch and VPC Flow Logs

## 🤝 Contributing

To extend this solution:

1. Modify `terraform/modules/vpc/` for infrastructure changes
2. Update `roles/aws_tfvpc/` for orchestration changes
3. Add new templates in `roles/aws_tfvpc/templates/`
4. Update documentation

## 📖 Additional Resources

- [AWS VPC Documentation](https://docs.aws.amazon.com/vpc/)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/)
- [Ansible AWS Collection](https://docs.ansible.com/ansible/latest/collections/amazon/aws/)

This solution provides a production-ready foundation for AWS VPC infrastructure with proper automation, documentation, and best practices.
