# VPC Terraform Module

This module creates a complete AWS VPC infrastructure with public, private, and database subnets across multiple availability zones.

## Features

- **VPC**: Creates a VPC with configurable CIDR block
- **Subnets**: Creates public, private, and database subnets across all available AZs
- **Internet Gateway**: Provides internet access for public subnets
- **NAT Gateways**: Provides outbound internet access for private subnets
- **Route Tables**: Properly configured routing for all subnet types
- **Elastic IPs**: For NAT gateways
- **Tagging**: Consistent tagging strategy following organizational standards

## Architecture

```
VPC (10.0.0.0/16)
├── Public Subnets (********/24, ********/24, ********/24)
│   ├── Internet Gateway
│   └── Public Route Table
├── Private Subnets (*********/24, *********/24, *********/24)
│   ├── NAT Gateway(s)
│   └── Private Route Table(s)
└── Database Subnets (*********/24, *********/24, *********/24)
    └── Database Route Table
```

## Usage

### Basic Usage

```hcl
module "vpc" {
  source = "./modules/vpc"
  
  common_data = {
    prefix  = "myapp-dev"
    az_list = ["us-east-1a", "us-east-1b", "us-east-1c"]
    tags = {
      Environment = "development"
      Project     = "myapp"
    }
  }
  
  vpc_cidr = "10.0.0.0/16"
}
```

### Advanced Usage

```hcl
module "vpc" {
  source = "./modules/vpc"
  
  common_data = var.common_data
  
  # VPC Configuration
  vpc_cidr             = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  # NAT Gateway Configuration
  create_nat_gateway     = true
  single_nat_gateway     = false  # Create one NAT gateway per AZ for HA
  one_nat_gateway_per_az = true
  
  # Additional tags
  vpc_tags = {
    Purpose = "Production workloads"
  }
  
  public_subnet_tags = {
    kubernetes.io/role/elb = "1"
  }
  
  private_subnet_tags = {
    kubernetes.io/role/internal-elb = "1"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| common_data | Common data containing prefix, tags, and az_list | `any` | n/a | yes |
| vpc_cidr | CIDR block for VPC | `string` | `"10.0.0.0/16"` | no |
| enable_dns_hostnames | Enable DNS hostnames in the VPC | `bool` | `true` | no |
| enable_dns_support | Enable DNS support in the VPC | `bool` | `true` | no |
| create_igw | Create Internet Gateway | `bool` | `true` | no |
| create_nat_gateway | Create NAT Gateway for private subnets | `bool` | `true` | no |
| single_nat_gateway | Use single NAT gateway for all private subnets | `bool` | `false` | no |
| one_nat_gateway_per_az | Create one NAT gateway per availability zone | `bool` | `true` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the VPC |
| vpc_arn | ARN of the VPC |
| vpc_cidr_block | CIDR block of the VPC |
| public_subnets | List of IDs of public subnets |
| private_subnets | List of IDs of private subnets |
| database_subnets | List of IDs of database subnets |
| igw_id | ID of the Internet Gateway |
| nat_ids | List of IDs of the NAT Gateways |
| nat_public_ips | List of public Elastic IPs of NAT Gateways |
| vpc_data | Complete VPC data for use by other modules |

## Subnet Design

The module automatically calculates subnet CIDRs based on the VPC CIDR:

- **Public subnets**: Start from .1.0/24 (********/24, ********/24, etc.)
- **Private subnets**: Start from .11.0/24 (*********/24, *********/24, etc.)
- **Database subnets**: Start from .21.0/24 (*********/24, *********/24, etc.)

## Cost Optimization

- Set `single_nat_gateway = true` to use only one NAT gateway (reduces costs but eliminates AZ redundancy)
- Set `create_nat_gateway = false` if private subnets don't need internet access

## High Availability

- By default, creates one NAT gateway per availability zone
- Distributes subnets across all available AZs
- Separate route tables for proper traffic isolation

## Security Considerations

- Database subnets have no direct internet access
- Private subnets access internet through NAT gateways only
- Proper subnet tagging for Kubernetes integration
- Default security group restrictions apply

## Examples

See the `examples/` directory for complete usage examples:

- `examples/basic-vpc/` - Simple VPC setup
- `examples/production-vpc/` - Production-ready VPC with all features
- `examples/cost-optimized-vpc/` - Cost-optimized VPC with single NAT gateway

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | >= 5.0 |

## Compatibility

This module is compatible with:
- Terraform >= 1.0
- AWS Provider >= 5.0
- All AWS regions
- All availability zones

## Contributing

When contributing to this module:

1. Follow the existing code style
2. Update documentation for any new variables or outputs
3. Test in multiple regions and AZ configurations
4. Ensure backward compatibility
