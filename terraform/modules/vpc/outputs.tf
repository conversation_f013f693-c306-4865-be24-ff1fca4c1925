# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        outputs.tf                                                        #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC module outputs                         #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Outputs for VPC module to be used by other modules                #
#                                                                                 #
# ------------------------------------------------------------------------------- #

#-----------------------------------------------#
# VPC Outputs                                   #
#-----------------------------------------------#
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "default_security_group_id" {
  description = "ID of the default security group"
  value       = aws_vpc.main.default_security_group_id
}

output "default_route_table_id" {
  description = "ID of the default route table"
  value       = aws_vpc.main.default_route_table_id
}

#-----------------------------------------------#
# Internet Gateway Outputs                      #
#-----------------------------------------------#
output "igw_id" {
  description = "ID of the Internet Gateway"
  value       = var.create_igw ? aws_internet_gateway.main[0].id : null
}

output "igw_arn" {
  description = "ARN of the Internet Gateway"
  value       = var.create_igw ? aws_internet_gateway.main[0].arn : null
}

#-----------------------------------------------#
# Subnet Outputs                                #
#-----------------------------------------------#
output "public_subnets" {
  description = "List of IDs of public subnets"
  value       = aws_subnet.public[*].id
}

output "public_subnet_arns" {
  description = "List of ARNs of public subnets"
  value       = aws_subnet.public[*].arn
}

output "public_subnets_cidr_blocks" {
  description = "List of CIDR blocks of public subnets"
  value       = aws_subnet.public[*].cidr_block
}

output "private_subnets" {
  description = "List of IDs of private subnets"
  value       = aws_subnet.private[*].id
}

output "private_subnet_arns" {
  description = "List of ARNs of private subnets"
  value       = aws_subnet.private[*].arn
}

output "private_subnets_cidr_blocks" {
  description = "List of CIDR blocks of private subnets"
  value       = aws_subnet.private[*].cidr_block
}

output "database_subnets" {
  description = "List of IDs of database subnets"
  value       = aws_subnet.database[*].id
}

output "database_subnet_arns" {
  description = "List of ARNs of database subnets"
  value       = aws_subnet.database[*].arn
}

output "database_subnets_cidr_blocks" {
  description = "List of CIDR blocks of database subnets"
  value       = aws_subnet.database[*].cidr_block
}

#-----------------------------------------------#
# NAT Gateway Outputs                           #
#-----------------------------------------------#
output "nat_ids" {
  description = "List of IDs of the NAT Gateways"
  value       = aws_nat_gateway.main[*].id
}

output "nat_public_ips" {
  description = "List of public Elastic IPs of NAT Gateways"
  value       = aws_eip.nat[*].public_ip
}

output "natgw_ids" {
  description = "List of NAT Gateway IDs"
  value       = aws_nat_gateway.main[*].id
}

#-----------------------------------------------#
# Route Table Outputs                           #
#-----------------------------------------------#
output "public_route_table_ids" {
  description = "List of IDs of public route tables"
  value       = [aws_route_table.public.id]
}

output "private_route_table_ids" {
  description = "List of IDs of private route tables"
  value       = aws_route_table.private[*].id
}

output "database_route_table_ids" {
  description = "List of IDs of database route tables"
  value       = [aws_route_table.database.id]
}

#-----------------------------------------------#
# Availability Zone Outputs                     #
#-----------------------------------------------#
output "azs" {
  description = "List of availability zones used"
  value       = local.availability_zones
}

#-----------------------------------------------#
# Comprehensive VPC Data Output                 #
#-----------------------------------------------#
output "vpc_data" {
  description = "Complete VPC data for use by other modules"
  value = {
    # VPC Information
    vpc_id                    = aws_vpc.main.id
    vpc_arn                   = aws_vpc.main.arn
    vpc_cidr_block           = aws_vpc.main.cidr_block
    default_security_group_id = aws_vpc.main.default_security_group_id
    default_route_table_id   = aws_vpc.main.default_route_table_id
    
    # Internet Gateway
    igw_id  = var.create_igw ? aws_internet_gateway.main[0].id : null
    igw_arn = var.create_igw ? aws_internet_gateway.main[0].arn : null
    
    # Subnets
    public_subnets              = aws_subnet.public[*].id
    public_subnet_arns          = aws_subnet.public[*].arn
    public_subnets_cidr_blocks  = aws_subnet.public[*].cidr_block
    private_subnets             = aws_subnet.private[*].id
    private_subnet_arns         = aws_subnet.private[*].arn
    private_subnets_cidr_blocks = aws_subnet.private[*].cidr_block
    database_subnets            = aws_subnet.database[*].id
    database_subnet_arns        = aws_subnet.database[*].arn
    database_subnets_cidr_blocks = aws_subnet.database[*].cidr_block
    
    # NAT Gateways
    nat_ids        = aws_nat_gateway.main[*].id
    nat_public_ips = aws_eip.nat[*].public_ip
    
    # Route Tables
    public_route_table_ids   = [aws_route_table.public.id]
    private_route_table_ids  = aws_route_table.private[*].id
    database_route_table_ids = [aws_route_table.database.id]
    
    # Availability Zones
    azs = local.availability_zones
    
    # Metadata
    name_prefix = var.common_data.prefix
    tags        = local.common_tags
  }
}
