# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.tf                                                           #
# Version:                                                                        #
#               2024-10-02 AI. Created comprehensive VPC module                   #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Creates a complete VPC infrastructure with public/private subnets #
#               internet gateway, NAT gateways, and route tables                  #
#                                                                                 #
# ------------------------------------------------------------------------------- #

terraform {
  required_providers { 
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.33"
    }
  }
}

#-----------------------------------------------#
# Variables Section                             #
#-----------------------------------------------#
variable "common_data" {
  description = "Common data passed from parent module"
  type        = any
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = true
}

variable "create_igw" {
  description = "Create Internet Gateway"
  type        = bool
  default     = true
}

variable "create_nat_gateway" {
  description = "Create NAT Gateway for private subnets"
  type        = bool
  default     = true
}

variable "single_nat_gateway" {
  description = "Use single NAT gateway for all private subnets"
  type        = bool
  default     = false
}

#-----------------------------------------------#
# Local Variables                               #
#-----------------------------------------------#
locals {
  # Get availability zones from common data
  availability_zones = var.common_data.az_list
  az_count          = length(local.availability_zones)
  
  # Calculate subnet CIDRs
  # Public subnets: ********/24, ********/24, ********/24
  # Private subnets: *********/24, *********/24, *********/24
  # Database subnets: *********/24, *********/24, *********/24
  
  public_subnet_cidrs = [
    for i in range(local.az_count) : cidrsubnet(var.vpc_cidr, 8, i + 1)
  ]
  
  private_subnet_cidrs = [
    for i in range(local.az_count) : cidrsubnet(var.vpc_cidr, 8, i + 11)
  ]
  
  database_subnet_cidrs = [
    for i in range(local.az_count) : cidrsubnet(var.vpc_cidr, 8, i + 21)
  ]
  
  # Common tags
  common_tags = merge(
    var.common_data.tags,
    {
      Module = "vpc"
      ManagedBy = "terraform"
    }
  )
}

#-----------------------------------------------#
# VPC Resource                                  #
#-----------------------------------------------#
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-vpc"
      Description = "Main VPC for ${var.common_data.service_name}"
    }
  )
}

#-----------------------------------------------#
# Internet Gateway                              #
#-----------------------------------------------#
resource "aws_internet_gateway" "main" {
  count  = var.create_igw ? 1 : 0
  vpc_id = aws_vpc.main.id

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-igw"
      Description = "Internet Gateway for ${var.common_data.prefix}"
    }
  )
}

#-----------------------------------------------#
# Public Subnets                                #
#-----------------------------------------------#
resource "aws_subnet" "public" {
  count = local.az_count

  vpc_id                  = aws_vpc.main.id
  cidr_block              = local.public_subnet_cidrs[count.index]
  availability_zone       = local.availability_zones[count.index]
  map_public_ip_on_launch = true

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-public-${substr(local.availability_zones[count.index], -1, 1)}"
      Type = "public"
      Tier = "public"
      kubernetes.io/role/elb = "1"
    }
  )
}

#-----------------------------------------------#
# Private Subnets                               #
#-----------------------------------------------#
resource "aws_subnet" "private" {
  count = local.az_count

  vpc_id            = aws_vpc.main.id
  cidr_block        = local.private_subnet_cidrs[count.index]
  availability_zone = local.availability_zones[count.index]

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-private-${substr(local.availability_zones[count.index], -1, 1)}"
      Type = "private"
      Tier = "private"
      kubernetes.io/role/internal-elb = "1"
    }
  )
}

#-----------------------------------------------#
# Database Subnets                              #
#-----------------------------------------------#
resource "aws_subnet" "database" {
  count = local.az_count

  vpc_id            = aws_vpc.main.id
  cidr_block        = local.database_subnet_cidrs[count.index]
  availability_zone = local.availability_zones[count.index]

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-database-${substr(local.availability_zones[count.index], -1, 1)}"
      Type = "database"
      Tier = "database"
    }
  )
}

#-----------------------------------------------#
# Elastic IPs for NAT Gateways                  #
#-----------------------------------------------#
resource "aws_eip" "nat" {
  count = var.create_nat_gateway ? (var.single_nat_gateway ? 1 : local.az_count) : 0

  domain = "vpc"
  depends_on = [aws_internet_gateway.main]

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-nat-eip-${count.index + 1}"
      Description = "Elastic IP for NAT Gateway ${count.index + 1}"
    }
  )
}

#-----------------------------------------------#
# NAT Gateways                                   #
#-----------------------------------------------#
resource "aws_nat_gateway" "main" {
  count = var.create_nat_gateway ? (var.single_nat_gateway ? 1 : local.az_count) : 0

  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = aws_subnet.public[count.index].id
  depends_on    = [aws_internet_gateway.main]

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-nat-${count.index + 1}"
      Description = "NAT Gateway ${count.index + 1}"
    }
  )
}

#-----------------------------------------------#
# Route Tables                                   #
#-----------------------------------------------#

# Public Route Table
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-public-rt"
      Type = "public"
    }
  )
}

# Public Route to Internet Gateway
resource "aws_route" "public_internet_gateway" {
  count = var.create_igw ? 1 : 0

  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.main[0].id

  timeouts {
    create = "5m"
  }
}

# Associate public subnets with public route table
resource "aws_route_table_association" "public" {
  count = local.az_count

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

# Private Route Tables
resource "aws_route_table" "private" {
  count = var.create_nat_gateway ? (var.single_nat_gateway ? 1 : local.az_count) : 1

  vpc_id = aws_vpc.main.id

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-private-rt-${count.index + 1}"
      Type = "private"
    }
  )
}

# Private Routes to NAT Gateway
resource "aws_route" "private_nat_gateway" {
  count = var.create_nat_gateway ? (var.single_nat_gateway ? 1 : local.az_count) : 0

  route_table_id         = aws_route_table.private[count.index].id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.main[count.index].id

  timeouts {
    create = "5m"
  }
}

# Associate private subnets with private route tables
resource "aws_route_table_association" "private" {
  count = local.az_count

  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[var.single_nat_gateway ? 0 : count.index].id
}

# Database Route Table
resource "aws_route_table" "database" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    local.common_tags,
    {
      Name = "${var.common_data.prefix}-database-rt"
      Type = "database"
    }
  )
}

# Associate database subnets with database route table
resource "aws_route_table_association" "database" {
  count = local.az_count

  subnet_id      = aws_subnet.database[count.index].id
  route_table_id = aws_route_table.database.id
}
