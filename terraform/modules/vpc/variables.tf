# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        variables.tf                                                      #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC module variables                       #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Variable definitions for VPC module                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

#-----------------------------------------------#
# Required Variables                            #
#-----------------------------------------------#
variable "common_data" {
  description = "Common data passed from parent module containing account, region, environment, tags, etc."
  type        = any
  
  validation {
    condition = can(var.common_data.prefix) && can(var.common_data.tags) && can(var.common_data.az_list)
    error_message = "common_data must contain prefix, tags, and az_list."
  }
}

#-----------------------------------------------#
# VPC Configuration Variables                   #
#-----------------------------------------------#
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
  
  validation {
    condition = can(cidrhost(var.vpc_cidr, 0))
    error_message = "VPC CIDR must be a valid IPv4 CIDR block."
  }
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = true
}

variable "instance_tenancy" {
  description = "Tenancy of instances spin up within VPC"
  type        = string
  default     = "default"
  
  validation {
    condition = contains(["default", "dedicated"], var.instance_tenancy)
    error_message = "Instance tenancy must be either 'default' or 'dedicated'."
  }
}

#-----------------------------------------------#
# Internet Gateway Variables                    #
#-----------------------------------------------#
variable "create_igw" {
  description = "Create Internet Gateway"
  type        = bool
  default     = true
}

#-----------------------------------------------#
# NAT Gateway Variables                         #
#-----------------------------------------------#
variable "create_nat_gateway" {
  description = "Create NAT Gateway for private subnets"
  type        = bool
  default     = true
}

variable "single_nat_gateway" {
  description = "Use single NAT gateway for all private subnets (cost optimization)"
  type        = bool
  default     = false
}

variable "one_nat_gateway_per_az" {
  description = "Create one NAT gateway per availability zone (high availability)"
  type        = bool
  default     = true
}

variable "reuse_nat_ips" {
  description = "Reuse existing Elastic IPs for NAT gateways"
  type        = bool
  default     = false
}

variable "external_nat_ip_ids" {
  description = "List of EIP IDs to be assigned to the NAT Gateways (used in combination with reuse_nat_ips)"
  type        = list(string)
  default     = []
}

#-----------------------------------------------#
# Subnet Configuration Variables                #
#-----------------------------------------------#
variable "public_subnet_suffix" {
  description = "Suffix to append to public subnet names"
  type        = string
  default     = "public"
}

variable "private_subnet_suffix" {
  description = "Suffix to append to private subnet names"
  type        = string
  default     = "private"
}

variable "database_subnet_suffix" {
  description = "Suffix to append to database subnet names"
  type        = string
  default     = "database"
}

variable "public_subnet_tags" {
  description = "Additional tags for public subnets"
  type        = map(string)
  default     = {}
}

variable "private_subnet_tags" {
  description = "Additional tags for private subnets"
  type        = map(string)
  default     = {}
}

variable "database_subnet_tags" {
  description = "Additional tags for database subnets"
  type        = map(string)
  default     = {}
}

variable "map_public_ip_on_launch" {
  description = "Specify true to indicate that instances launched into the public subnet should be assigned a public IP address"
  type        = bool
  default     = true
}

#-----------------------------------------------#
# Route Table Variables                         #
#-----------------------------------------------#
variable "public_route_table_tags" {
  description = "Additional tags for public route tables"
  type        = map(string)
  default     = {}
}

variable "private_route_table_tags" {
  description = "Additional tags for private route tables"
  type        = map(string)
  default     = {}
}

variable "database_route_table_tags" {
  description = "Additional tags for database route tables"
  type        = map(string)
  default     = {}
}

#-----------------------------------------------#
# VPC Flow Logs Variables                       #
#-----------------------------------------------#
variable "enable_flow_log" {
  description = "Enable VPC Flow Logs"
  type        = bool
  default     = false
}

variable "flow_log_destination_type" {
  description = "Type of flow log destination. Can be s3 or cloud-watch-logs"
  type        = string
  default     = "cloud-watch-logs"
  
  validation {
    condition = contains(["s3", "cloud-watch-logs"], var.flow_log_destination_type)
    error_message = "Flow log destination type must be either 's3' or 'cloud-watch-logs'."
  }
}

variable "flow_log_destination_arn" {
  description = "ARN of the destination for VPC Flow Logs"
  type        = string
  default     = ""
}

#-----------------------------------------------#
# VPC Endpoints Variables                       #
#-----------------------------------------------#
variable "enable_s3_endpoint" {
  description = "Enable S3 VPC Endpoint"
  type        = bool
  default     = false
}

variable "enable_dynamodb_endpoint" {
  description = "Enable DynamoDB VPC Endpoint"
  type        = bool
  default     = false
}

#-----------------------------------------------#
# DHCP Options Variables                        #
#-----------------------------------------------#
variable "enable_dhcp_options" {
  description = "Enable DHCP options"
  type        = bool
  default     = false
}

variable "dhcp_options_domain_name" {
  description = "Domain name for DHCP options"
  type        = string
  default     = ""
}

variable "dhcp_options_domain_name_servers" {
  description = "Domain name servers for DHCP options"
  type        = list(string)
  default     = ["AmazonProvidedDNS"]
}

#-----------------------------------------------#
# Additional Tags                               #
#-----------------------------------------------#
variable "vpc_tags" {
  description = "Additional tags for the VPC"
  type        = map(string)
  default     = {}
}

variable "igw_tags" {
  description = "Additional tags for the Internet Gateway"
  type        = map(string)
  default     = {}
}

variable "nat_gateway_tags" {
  description = "Additional tags for NAT gateways"
  type        = map(string)
  default     = {}
}

variable "nat_eip_tags" {
  description = "Additional tags for NAT gateway Elastic IPs"
  type        = map(string)
  default     = {}
}
