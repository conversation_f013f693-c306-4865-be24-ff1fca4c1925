---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_cleanup.yml                                             #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC cleanup tasks                          #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Cleans up temporary files and optionally preserves artifacts      #
# Inputs:                                                                         #
#               vpc_build_artifacts: Build artifacts from previous tasks          #
# Outputs:                                                                        #
#               Cleaned up temporary files                                        #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      tempdir: "{{ vpc_build_artifacts.tempdir }}"
      preserve_artifacts: "{{ aws_tfvpc_preserve_artifacts | default(false) }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
# Preserve important artifacts if requested                                      #
# ------------------------------------------------------------------------------- #
- name: Create artifacts preservation directory
  ansible.builtin.file:
    path: "{{ aws_tfvpc_artifacts_dir | default('./vpc_artifacts') }}"
    state: directory
    mode: '0755'
  when: aws_tfvpc_preserve_artifacts | default(false)

- name: Preserve VPC Terraform state file
  ansible.builtin.copy:
    src: "{{ vpc_build_artifacts.tempdir }}/terraform.tfstate"
    dest: "{{ aws_tfvpc_artifacts_dir | default('./vpc_artifacts') }}/terraform.tfstate"
    mode: '0644'
  when: 
    - aws_tfvpc_preserve_artifacts | default(false)
    - (vpc_build_artifacts.tempdir + '/terraform.tfstate') is file

- name: Preserve VPC data file
  ansible.builtin.copy:
    src: "{{ vpc_build_artifacts.tempdir }}/vpc_data.json"
    dest: "{{ aws_tfvpc_artifacts_dir | default('./vpc_artifacts') }}/vpc_data.json"
    mode: '0644'
  when: 
    - aws_tfvpc_preserve_artifacts | default(false)
    - (vpc_build_artifacts.tempdir + '/vpc_data.json') is file

- name: Preserve Terraform configuration files
  ansible.builtin.copy:
    src: "{{ item }}"
    dest: "{{ aws_tfvpc_artifacts_dir | default('./vpc_artifacts') }}/{{ item | basename }}"
    mode: '0644'
  with_fileglob:
    - "{{ vpc_build_artifacts.tempdir }}/*.tf"
    - "{{ vpc_build_artifacts.tempdir }}/*.tfvars"
  when: aws_tfvpc_preserve_artifacts | default(false)

# ------------------------------------------------------------------------------- #
# List files before cleanup for debugging                                        #
# ------------------------------------------------------------------------------- #
- name: List files in temporary directory before cleanup
  ansible.builtin.find:
    paths: "{{ vpc_build_artifacts.tempdir }}"
    file_type: file
  register: temp_files_before_cleanup

- name: Display files to be cleaned up
  ansible.builtin.debug:
    msg: "Files in temporary directory: {{ temp_files_before_cleanup.files | map(attribute='path') | list }}"
    verbosity: 2

# ------------------------------------------------------------------------------- #
# Clean up temporary directory                                                   #
# ------------------------------------------------------------------------------- #
- name: Remove sensitive credentials file
  ansible.builtin.file:
    path: "{{ vpc_build_artifacts.tempdir }}/credentials"
    state: absent

- name: Clean up temporary Terraform files
  ansible.builtin.file:
    path: "{{ vpc_build_artifacts.tempdir }}"
    state: absent
  when: not (aws_tfvpc_preserve_tempdir | default(false))

# ------------------------------------------------------------------------------- #
# Display cleanup summary                                                        #
# ------------------------------------------------------------------------------- #
- name: Display cleanup summary
  ansible.builtin.debug:
    msg: |
      VPC Cleanup Summary:
      ===================
      {% if aws_tfvpc_preserve_artifacts | default(false) %}
      Artifacts preserved in: {{ aws_tfvpc_artifacts_dir | default('./vpc_artifacts') }}
      {% endif %}
      {% if aws_tfvpc_preserve_tempdir | default(false) %}
      Temporary directory preserved: {{ vpc_build_artifacts.tempdir }}
      {% else %}
      Temporary directory cleaned up: {{ vpc_build_artifacts.tempdir }}
      {% endif %}
      Credentials file removed for security

# ------------------------------------------------------------------------------- #
# Set cleanup completion flag                                                    #
# ------------------------------------------------------------------------------- #
- name: Set cleanup completion status
  ansible.builtin.set_fact:
    vpc_cleanup_completed: true
    vpc_cleanup_summary:
      artifacts_preserved: "{{ aws_tfvpc_preserve_artifacts | default(false) }}"
      tempdir_preserved: "{{ aws_tfvpc_preserve_tempdir | default(false) }}"
      artifacts_location: "{{ aws_tfvpc_artifacts_dir | default('./vpc_artifacts') if (aws_tfvpc_preserve_artifacts | default(false)) else 'none' }}"
      tempdir_location: "{{ vpc_build_artifacts.tempdir if (aws_tfvpc_preserve_tempdir | default(false)) else 'cleaned' }}"

- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      cleanup_completed: "{{ vpc_cleanup_completed }}"
      artifacts_preserved: "{{ aws_tfvpc_preserve_artifacts | default(false) }}"
      tempdir_preserved: "{{ aws_tfvpc_preserve_tempdir | default(false) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
