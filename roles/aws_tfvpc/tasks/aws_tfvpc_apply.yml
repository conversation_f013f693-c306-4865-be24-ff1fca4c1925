---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_apply.yml                                               #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC apply tasks                            #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Applies Terraform configuration to create VPC infrastructure      #
# Inputs:                                                                         #
#               vpc_build_artifacts: Build artifacts from previous task           #
# Outputs:                                                                        #
#               VPC infrastructure created in AWS                                 #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      tempdir: "{{ vpc_build_artifacts.tempdir }}"
      plan_file: "{{ vpc_build_artifacts.plan_file }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
# Verify build artifacts exist                                                   #
# ------------------------------------------------------------------------------- #
- name: Verify Terraform plan file exists
  ansible.builtin.stat:
    path: "{{ vpc_build_artifacts.plan_file }}"
  register: plan_file_stat

- name: Fail if plan file doesn't exist
  ansible.builtin.fail:
    msg: "Terraform plan file not found: {{ vpc_build_artifacts.plan_file }}"
  when: not plan_file_stat.stat.exists

# ------------------------------------------------------------------------------- #
# Apply Terraform configuration                                                  #
# ------------------------------------------------------------------------------- #
- name: Apply VPC Terraform configuration
  community.general.terraform:
    project_path: "{{ vpc_build_artifacts.tempdir }}"
    state: present
    plan_file: "{{ vpc_build_artifacts.plan_file }}"
    force_init: false
  environment:
    AWS_SHARED_CREDENTIALS_FILE: "{{ vpc_build_artifacts.tempdir }}/credentials"
    AWS_PROFILE: "{{ aws_common_credentials.init.profile | default('default') }}"
  register: terraform_apply_result

- name: Display Terraform apply result
  ansible.builtin.debug:
    var: terraform_apply_result
    verbosity: 1

# ------------------------------------------------------------------------------- #
# Handle apply failures                                                          #
# ------------------------------------------------------------------------------- #
- name: Check if Terraform apply was successful
  ansible.builtin.fail:
    msg: "Terraform apply failed: {{ terraform_apply_result.msg | default('Unknown error') }}"
  when: 
    - terraform_apply_result.failed is defined
    - terraform_apply_result.failed | bool

# ------------------------------------------------------------------------------- #
# Store apply results                                                            #
# ------------------------------------------------------------------------------- #
- name: Store VPC apply results
  ansible.builtin.set_fact:
    vpc_apply_result: "{{ terraform_apply_result }}"

- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      apply_successful: "{{ not (terraform_apply_result.failed | default(false)) }}"
      changes_made: "{{ terraform_apply_result.changed | default(false) }}"
      stdout_lines: "{{ terraform_apply_result.stdout_lines | default([]) | length }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
