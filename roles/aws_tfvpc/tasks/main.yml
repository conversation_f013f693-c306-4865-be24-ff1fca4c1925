---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC management tasks                       #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Ansible main entry point for the aws_tfvpc role.                  #
#               Manages AWS VPC infrastructure using Terraform modules.           #
# Inputs:                                                                         #
#               aws_tfvpc_config: VPC configuration parameters                    #
# Outputs:                                                                        #
#               VPC infrastructure created in AWS                                 #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      aws_tfvpc_config: "{{ aws_tfvpc_config | default({}) }}"
      aws_common_credentials: "{{ aws_common_credentials }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
# Validate required parameters                                                   #
# ------------------------------------------------------------------------------- #
- name: Validate VPC configuration parameters
  ansible.builtin.assert:
    that:
      - aws_tfvpc_config is defined
      - aws_tfvpc_config.vpc_cidr is defined
      - aws_tfvpc_config.vpc_cidr | length > 0
      - aws_common_credentials is defined
    fail_msg: "Required VPC configuration parameters are missing"
    success_msg: "VPC configuration validation passed"

# ------------------------------------------------------------------------------- #
# Set default VPC configuration values                                           #
# ------------------------------------------------------------------------------- #
- name: Set VPC configuration defaults
  ansible.builtin.set_fact:
    vpc_config: "{{ default_vpc_config | combine(aws_tfvpc_config, recursive=true) }}"
  vars:
    default_vpc_config:
      vpc_cidr: "10.0.0.0/16"
      enable_dns_hostnames: true
      enable_dns_support: true
      create_igw: true
      create_nat_gateway: true
      single_nat_gateway: false
      one_nat_gateway_per_az: true
      map_public_ip_on_launch: true
      enable_flow_log: false
      enable_s3_endpoint: false
      enable_dynamodb_endpoint: false
      tags: {}

# ------------------------------------------------------------------------------- #
# Execute VPC creation tasks                                                      #
# ------------------------------------------------------------------------------- #
- name: Execute VPC build task
  ansible.builtin.include_tasks: "{{ item }}.yml"
  loop: 
    - aws_tfvpc_build
    - aws_tfvpc_apply
    - aws_tfvpc_outputs
    - aws_tfvpc_cleanup

# ------------------------------------------------------------------------------- #
# Output task completion                                                          #
# ------------------------------------------------------------------------------- #
- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      vpc_id: "{{ vpc_outputs.vpc_id | default('') }}"
      vpc_cidr: "{{ vpc_config.vpc_cidr }}"
      public_subnets: "{{ vpc_outputs.public_subnets | default([]) }}"
      private_subnets: "{{ vpc_outputs.private_subnets | default([]) }}"
      database_subnets: "{{ vpc_outputs.database_subnets | default([]) }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
