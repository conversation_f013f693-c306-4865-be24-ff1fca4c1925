---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_outputs.yml                                             #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC outputs tasks                          #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Retrieves Terraform outputs from VPC infrastructure               #
# Inputs:                                                                         #
#               vpc_build_artifacts: Build artifacts from previous tasks          #
# Outputs:                                                                        #
#               vpc_outputs: VPC infrastructure details                           #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      tempdir: "{{ vpc_build_artifacts.tempdir }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
# Retrieve Terraform outputs                                                     #
# ------------------------------------------------------------------------------- #
- name: Get VPC Terraform outputs
  community.general.terraform:
    project_path: "{{ vpc_build_artifacts.tempdir }}"
    state: present
    force_init: false
  environment:
    AWS_SHARED_CREDENTIALS_FILE: "{{ vpc_build_artifacts.tempdir }}/credentials"
    AWS_PROFILE: "{{ aws_common_credentials.init.profile | default('default') }}"
  register: terraform_outputs_result

- name: Display raw Terraform outputs
  ansible.builtin.debug:
    var: terraform_outputs_result.outputs
    verbosity: 2

# ------------------------------------------------------------------------------- #
# Parse and structure VPC outputs                                                #
# ------------------------------------------------------------------------------- #
- name: Extract VPC outputs
  ansible.builtin.set_fact:
    vpc_outputs:
      # VPC Information
      vpc_id: "{{ terraform_outputs_result.outputs.vpc_id.value | default('') }}"
      vpc_arn: "{{ terraform_outputs_result.outputs.vpc_arn.value | default('') }}"
      vpc_cidr_block: "{{ terraform_outputs_result.outputs.vpc_cidr_block.value | default('') }}"
      default_security_group_id: "{{ terraform_outputs_result.outputs.default_security_group_id.value | default('') }}"
      default_route_table_id: "{{ terraform_outputs_result.outputs.default_route_table_id.value | default('') }}"
      
      # Internet Gateway
      igw_id: "{{ terraform_outputs_result.outputs.igw_id.value | default('') }}"
      igw_arn: "{{ terraform_outputs_result.outputs.igw_arn.value | default('') }}"
      
      # Subnets
      public_subnets: "{{ terraform_outputs_result.outputs.public_subnets.value | default([]) }}"
      public_subnet_arns: "{{ terraform_outputs_result.outputs.public_subnet_arns.value | default([]) }}"
      public_subnets_cidr_blocks: "{{ terraform_outputs_result.outputs.public_subnets_cidr_blocks.value | default([]) }}"
      
      private_subnets: "{{ terraform_outputs_result.outputs.private_subnets.value | default([]) }}"
      private_subnet_arns: "{{ terraform_outputs_result.outputs.private_subnet_arns.value | default([]) }}"
      private_subnets_cidr_blocks: "{{ terraform_outputs_result.outputs.private_subnets_cidr_blocks.value | default([]) }}"
      
      database_subnets: "{{ terraform_outputs_result.outputs.database_subnets.value | default([]) }}"
      database_subnet_arns: "{{ terraform_outputs_result.outputs.database_subnet_arns.value | default([]) }}"
      database_subnets_cidr_blocks: "{{ terraform_outputs_result.outputs.database_subnets_cidr_blocks.value | default([]) }}"
      
      # NAT Gateways
      nat_ids: "{{ terraform_outputs_result.outputs.nat_ids.value | default([]) }}"
      nat_public_ips: "{{ terraform_outputs_result.outputs.nat_public_ips.value | default([]) }}"
      
      # Route Tables
      public_route_table_ids: "{{ terraform_outputs_result.outputs.public_route_table_ids.value | default([]) }}"
      private_route_table_ids: "{{ terraform_outputs_result.outputs.private_route_table_ids.value | default([]) }}"
      database_route_table_ids: "{{ terraform_outputs_result.outputs.database_route_table_ids.value | default([]) }}"
      
      # Availability Zones
      azs: "{{ terraform_outputs_result.outputs.azs.value | default([]) }}"

# ------------------------------------------------------------------------------- #
# Create comprehensive VPC data structure                                        #
# ------------------------------------------------------------------------------- #
- name: Create comprehensive VPC data
  ansible.builtin.set_fact:
    vpc_data:
      metadata:
        created_by: "ansible-aws_tfvpc"
        created_at: "{{ ansible_date_time.iso8601 }}"
        terraform_workspace: "{{ vpc_build_artifacts.tempdir }}"
        configuration: "{{ vpc_build_artifacts.config }}"
        common_data: "{{ vpc_build_artifacts.common_data }}"
      
      infrastructure: "{{ vpc_outputs }}"
      
      summary:
        vpc_id: "{{ vpc_outputs.vpc_id }}"
        vpc_cidr: "{{ vpc_outputs.vpc_cidr_block }}"
        public_subnet_count: "{{ vpc_outputs.public_subnets | length }}"
        private_subnet_count: "{{ vpc_outputs.private_subnets | length }}"
        database_subnet_count: "{{ vpc_outputs.database_subnets | length }}"
        nat_gateway_count: "{{ vpc_outputs.nat_ids | length }}"
        availability_zones: "{{ vpc_outputs.azs | length }}"

# ------------------------------------------------------------------------------- #
# Display VPC creation summary                                                   #
# ------------------------------------------------------------------------------- #
- name: Display VPC creation summary
  ansible.builtin.debug:
    msg: |
      VPC Infrastructure Created Successfully:
      =====================================
      VPC ID: {{ vpc_outputs.vpc_id }}
      VPC CIDR: {{ vpc_outputs.vpc_cidr_block }}
      
      Subnets Created:
      - Public Subnets: {{ vpc_outputs.public_subnets | length }} ({{ vpc_outputs.public_subnets | join(', ') }})
      - Private Subnets: {{ vpc_outputs.private_subnets | length }} ({{ vpc_outputs.private_subnets | join(', ') }})
      - Database Subnets: {{ vpc_outputs.database_subnets | length }} ({{ vpc_outputs.database_subnets | join(', ') }})
      
      Network Components:
      - Internet Gateway: {{ vpc_outputs.igw_id }}
      - NAT Gateways: {{ vpc_outputs.nat_ids | length }} ({{ vpc_outputs.nat_ids | join(', ') }})
      - Availability Zones: {{ vpc_outputs.azs | join(', ') }}

# ------------------------------------------------------------------------------- #
# Save VPC data to file for future reference                                     #
# ------------------------------------------------------------------------------- #
- name: Save VPC data to file
  ansible.builtin.copy:
    content: "{{ vpc_data | to_nice_json }}"
    dest: "{{ vpc_build_artifacts.tempdir }}/vpc_data.json"
    mode: '0644'
  when: vpc_outputs.vpc_id | length > 0

- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      vpc_id: "{{ vpc_outputs.vpc_id }}"
      public_subnets_count: "{{ vpc_outputs.public_subnets | length }}"
      private_subnets_count: "{{ vpc_outputs.private_subnets | length }}"
      database_subnets_count: "{{ vpc_outputs.database_subnets | length }}"
      nat_gateways_count: "{{ vpc_outputs.nat_ids | length }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
