---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws_tfvpc_build.yml                                               #
# Version:                                                                        #
#               2024-10-02 AI. Created VPC build tasks                            #
# Create Date:  2024-10-02                                                        #
# Author:       AI Assistant                                                      #
# Description:                                                                    #
#               Builds Terraform configuration for VPC infrastructure             #
# Inputs:                                                                         #
#               vpc_config: VPC configuration parameters                          #
# Outputs:                                                                        #
#               Terraform files ready for execution                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: "{{ ansible_common_debug.task_entry.name }}"
  vars:
    inputs:
      vpc_config: "{{ vpc_config }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: "{{ ansible_common_debug.task_entry.verbosity }}"

# ------------------------------------------------------------------------------- #
# Create temporary directory for Terraform files                                 #
# ------------------------------------------------------------------------------- #
- name: Create temporary directory for VPC Terraform files
  ansible.builtin.tempfile:
    state: directory
    suffix: "_vpc_terraform"
  register: aws_tfvpc_tempdir

- name: Display temporary directory path
  ansible.builtin.debug:
    msg: "VPC Terraform files will be created in: {{ aws_tfvpc_tempdir.path }}"
    verbosity: 1

# ------------------------------------------------------------------------------- #
# Prepare common data for Terraform                                              #
# ------------------------------------------------------------------------------- #
- name: Prepare common data for VPC module
  ansible.builtin.set_fact:
    vpc_common_data:
      account: "{{ aws_account.id }}"
      region: "{{ aws_region.name }}"
      environment: "{{ aws_environment.id }}"
      prefix: "{{ aws_region.id }}-{{ aws_environment.id }}"
      prefix_global: "{{ aws_account.id }}-{{ aws_region.id }}-{{ aws_environment.id }}"
      service_name: "{{ aws_common.service_name | default('AWS Infrastructure') }}"
      service_id: "{{ aws_common.service_id | default('aws') }}"
      az_list: "{{ aws_region.az_list }}"
      aws_partition: "{{ aws_region.partition }}"
      tags: "{{ aws_common.tags | combine(vpc_config.tags) }}"

# ------------------------------------------------------------------------------- #
# Create Terraform configuration files from templates                            #
# ------------------------------------------------------------------------------- #
- name: Create VPC Terraform main configuration
  ansible.builtin.template:
    src: "{{ role_path }}/templates/vpc_main.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/main.tf"
    mode: '0644'
  vars:
    terraform_module_dir: "{{ role_path }}/../../terraform/modules"

- name: Create VPC Terraform providers configuration
  ansible.builtin.template:
    src: "{{ role_path }}/templates/vpc_providers.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/providers.tf"
    mode: '0644'

- name: Create VPC Terraform variables configuration
  ansible.builtin.template:
    src: "{{ role_path }}/templates/vpc_variables.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/variables.tf"
    mode: '0644'

- name: Create VPC Terraform outputs configuration
  ansible.builtin.template:
    src: "{{ role_path }}/templates/vpc_outputs.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/outputs.tf"
    mode: '0644'

- name: Create VPC Terraform backend configuration
  ansible.builtin.template:
    src: "{{ role_path }}/templates/vpc_terraform.tf.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/terraform.tf"
    mode: '0644'

- name: Create AWS credentials file for Terraform
  ansible.builtin.template:
    src: "{{ role_path }}/templates/vpc_credentials.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/credentials"
    mode: '0600'

# ------------------------------------------------------------------------------- #
# Create terraform.tfvars file with VPC configuration                            #
# ------------------------------------------------------------------------------- #
- name: Create terraform.tfvars file
  ansible.builtin.template:
    src: "{{ role_path }}/templates/vpc_terraform.tfvars.j2"
    dest: "{{ aws_tfvpc_tempdir.path }}/terraform.tfvars"
    mode: '0644'

# ------------------------------------------------------------------------------- #
# Display created files for debugging                                            #
# ------------------------------------------------------------------------------- #
- name: List created Terraform files
  ansible.builtin.find:
    paths: "{{ aws_tfvpc_tempdir.path }}"
    patterns: "*.tf,*.tfvars,credentials"
  register: terraform_files

- name: Display created Terraform files
  ansible.builtin.debug:
    msg: "Created Terraform files: {{ terraform_files.files | map(attribute='path') | list }}"
    verbosity: 1

# ------------------------------------------------------------------------------- #
# Validate Terraform configuration                                               #
# ------------------------------------------------------------------------------- #
- name: Initialize Terraform
  community.general.terraform:
    project_path: "{{ aws_tfvpc_tempdir.path }}"
    state: present
    force_init: true
  environment:
    AWS_SHARED_CREDENTIALS_FILE: "{{ aws_tfvpc_tempdir.path }}/credentials"
    AWS_PROFILE: "{{ aws_common_credentials.init.profile | default('default') }}"
  register: terraform_init_result

- name: Display Terraform initialization result
  ansible.builtin.debug:
    var: terraform_init_result
    verbosity: 2

- name: Validate Terraform configuration
  community.general.terraform:
    project_path: "{{ aws_tfvpc_tempdir.path }}"
    state: present
    plan_file: "{{ aws_tfvpc_tempdir.path }}/vpc.tfplan"
  environment:
    AWS_SHARED_CREDENTIALS_FILE: "{{ aws_tfvpc_tempdir.path }}/credentials"
    AWS_PROFILE: "{{ aws_common_credentials.init.profile | default('default') }}"
  register: terraform_plan_result

- name: Display Terraform plan result
  ansible.builtin.debug:
    msg: "Terraform plan completed successfully. Plan file created at: {{ aws_tfvpc_tempdir.path }}/vpc.tfplan"
    verbosity: 1

# ------------------------------------------------------------------------------- #
# Store build artifacts for next tasks                                           #
# ------------------------------------------------------------------------------- #
- name: Store VPC build artifacts
  ansible.builtin.set_fact:
    vpc_build_artifacts:
      tempdir: "{{ aws_tfvpc_tempdir.path }}"
      plan_file: "{{ aws_tfvpc_tempdir.path }}/vpc.tfplan"
      common_data: "{{ vpc_common_data }}"
      config: "{{ vpc_config }}"

- name: "{{ ansible_common_debug.task_exit.name }}"
  vars:
    outputs:
      tempdir: "{{ aws_tfvpc_tempdir.path }}"
      plan_file_exists: "{{ (aws_tfvpc_tempdir.path + '/vpc.tfplan') | ansible.builtin.file_exists }}"
  ansible.builtin.debug:
    var: outputs
    verbosity: "{{ ansible_common_debug.task_exit.verbosity }}"
