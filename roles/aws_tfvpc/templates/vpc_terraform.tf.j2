# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        terraform.tf (Generated by Ansible)                              #
# Version:                                                                        #
#               2024-10-02 AI. Generated Terraform backend configuration          #
# Create Date:  {{ ansible_date_time.iso8601 }}                                  #
# Author:       Ansible aws_tfvpc role                                           #
# Description:                                                                    #
#               Terraform backend configuration for VPC state management          #
#               Generated from template: vpc_terraform.tf.j2                     #
#                                                                                 #
# ------------------------------------------------------------------------------- #

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.33"
    }
  }
  
{% if aws_tfvpc_use_remote_state | default(true) %}
  # Remote state configuration
  backend "s3" {
    bucket         = "{{ aws_account.id }}-{{ aws_region.id }}-{{ aws_environment.id }}-tfstate"
    key            = "vpc/terraform.tfstate"
    region         = "{{ aws_region.name }}"
    encrypt        = true
    dynamodb_table = "{{ aws_account.id }}-{{ aws_region.id }}-{{ aws_environment.id }}-tfstate-lock"
    
    # Use the same profile as the provider
    profile                = "{{ aws_common_credentials.init.profile | default('default') }}"
    shared_credentials_file = "./credentials"
  }
{% else %}
  # Local state configuration (for development/testing)
  # State will be stored locally in terraform.tfstate file
{% endif %}
}
