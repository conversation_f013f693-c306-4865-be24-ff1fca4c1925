# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        terraform.tfvars (Generated by Ansible)                          #
# Version:                                                                        #
#               2024-10-02 AI. Generated Terraform variables file                 #
# Create Date:  {{ ansible_date_time.iso8601 }}                                  #
# Author:       Ansible aws_tfvpc role                                           #
# Description:                                                                    #
#               Terraform variable values for VPC infrastructure                  #
#               Generated from template: vpc_terraform.tfvars.j2                 #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Environment Configuration                                                       #
# ------------------------------------------------------------------------------- #
account     = "{{ aws_account.id }}"
region      = "{{ aws_region.name }}"
environment = "{{ aws_environment.id }}"

# ------------------------------------------------------------------------------- #
# VPC Configuration                                                               #
# ------------------------------------------------------------------------------- #
vpc_cidr             = "{{ vpc_config.vpc_cidr }}"
enable_dns_hostnames = {{ vpc_config.enable_dns_hostnames | lower }}
enable_dns_support   = {{ vpc_config.enable_dns_support | lower }}

# ------------------------------------------------------------------------------- #
# Internet Gateway Configuration                                                  #
# ------------------------------------------------------------------------------- #
create_igw = {{ vpc_config.create_igw | lower }}

# ------------------------------------------------------------------------------- #
# NAT Gateway Configuration                                                       #
# ------------------------------------------------------------------------------- #
create_nat_gateway     = {{ vpc_config.create_nat_gateway | lower }}
single_nat_gateway     = {{ vpc_config.single_nat_gateway | lower }}
one_nat_gateway_per_az = {{ vpc_config.one_nat_gateway_per_az | lower }}

# ------------------------------------------------------------------------------- #
# Subnet Configuration                                                            #
# ------------------------------------------------------------------------------- #
map_public_ip_on_launch = {{ vpc_config.map_public_ip_on_launch | lower }}

# ------------------------------------------------------------------------------- #
# VPC Flow Logs Configuration                                                     #
# ------------------------------------------------------------------------------- #
enable_flow_log = {{ vpc_config.enable_flow_log | lower }}
{% if vpc_config.flow_log_destination_type is defined %}
flow_log_destination_type = "{{ vpc_config.flow_log_destination_type }}"
{% endif %}
{% if vpc_config.flow_log_destination_arn is defined %}
flow_log_destination_arn = "{{ vpc_config.flow_log_destination_arn }}"
{% endif %}

# ------------------------------------------------------------------------------- #
# VPC Endpoints Configuration                                                     #
# ------------------------------------------------------------------------------- #
enable_s3_endpoint       = {{ vpc_config.enable_s3_endpoint | lower }}
enable_dynamodb_endpoint = {{ vpc_config.enable_dynamodb_endpoint | lower }}

# ------------------------------------------------------------------------------- #
# Additional Tags                                                                 #
# ------------------------------------------------------------------------------- #
{% if vpc_config.vpc_tags is defined and vpc_config.vpc_tags | length > 0 %}
vpc_tags = {{ vpc_config.vpc_tags | to_json }}
{% endif %}

{% if vpc_config.public_subnet_tags is defined and vpc_config.public_subnet_tags | length > 0 %}
public_subnet_tags = {{ vpc_config.public_subnet_tags | to_json }}
{% endif %}

{% if vpc_config.private_subnet_tags is defined and vpc_config.private_subnet_tags | length > 0 %}
private_subnet_tags = {{ vpc_config.private_subnet_tags | to_json }}
{% endif %}

{% if vpc_config.database_subnet_tags is defined and vpc_config.database_subnet_tags | length > 0 %}
database_subnet_tags = {{ vpc_config.database_subnet_tags | to_json }}
{% endif %}
