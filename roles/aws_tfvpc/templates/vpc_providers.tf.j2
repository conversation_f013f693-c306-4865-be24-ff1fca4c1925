# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        providers.tf (Generated by Ansible)                              #
# Version:                                                                        #
#               2024-10-02 AI. Generated VPC providers configuration              #
# Create Date:  {{ ansible_date_time.iso8601 }}                                  #
# Author:       Ansible aws_tfvpc role                                           #
# Description:                                                                    #
#               Terraform providers configuration for VPC infrastructure          #
#               Generated from template: vpc_providers.tf.j2                     #
#                                                                                 #
# ------------------------------------------------------------------------------- #

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.33"
    }
  }
}

# ------------------------------------------------------------------------------- #
# AWS Provider Configuration                                                     #
# ------------------------------------------------------------------------------- #
provider "aws" {
  alias  = "{{ aws_region.name }}"
  region = "{{ aws_region.name }}"
  
  # Credentials configuration
  profile                  = "{{ aws_common_credentials.init.profile | default('default') }}"
  shared_credentials_files = ["./credentials"]
  
  # Account validation
  allowed_account_ids = ["{{ aws_account.id }}"]
  
  # Retry configuration
  max_retries = 50
  
  # Default tags for all resources
  default_tags {
    tags = {
      ManagedBy     = "terraform"
      CreatedBy     = "ansible-aws_tfvpc"
      Account       = "{{ aws_account.id }}"
      Region        = "{{ aws_region.name }}"
      Environment   = "{{ aws_environment.id }}"
      Partition     = "{{ aws_region.partition }}"
    }
  }
}

# ------------------------------------------------------------------------------- #
# Additional Provider Aliases (if needed)                                        #
# ------------------------------------------------------------------------------- #

{% if aws_region.replica_region is defined %}
# Replica region provider for cross-region resources
provider "aws" {
  alias  = "replica"
  region = "{{ aws_region.replica_region }}"
  
  profile                  = "{{ aws_common_credentials.init.profile | default('default') }}"
  shared_credentials_files = ["./credentials"]
  allowed_account_ids      = ["{{ aws_account.id }}"]
  max_retries              = 50
  
  default_tags {
    tags = {
      ManagedBy     = "terraform"
      CreatedBy     = "ansible-aws_tfvpc"
      Account       = "{{ aws_account.id }}"
      Region        = "{{ aws_region.replica_region }}"
      Environment   = "{{ aws_environment.id }}"
      Partition     = "{{ aws_region.partition }}"
      ReplicaRegion = "true"
    }
  }
}
{% endif %}
