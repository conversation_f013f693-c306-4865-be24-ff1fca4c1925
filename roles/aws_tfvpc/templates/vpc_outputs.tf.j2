# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        outputs.tf (Generated by Ansible)                                #
# Version:                                                                        #
#               2024-10-02 AI. Generated VPC outputs configuration                #
# Create Date:  {{ ansible_date_time.iso8601 }}                                  #
# Author:       Ansible aws_tfvpc role                                           #
# Description:                                                                    #
#               Terraform outputs for VPC infrastructure                          #
#               Generated from template: vpc_outputs.tf.j2                       #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# VPC Outputs                                                                     #
# ------------------------------------------------------------------------------- #
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = module.vpc.vpc_arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "default_security_group_id" {
  description = "ID of the default security group"
  value       = module.vpc.default_security_group_id
}

output "default_route_table_id" {
  description = "ID of the default route table"
  value       = module.vpc.default_route_table_id
}

# ------------------------------------------------------------------------------- #
# Internet Gateway Outputs                                                       #
# ------------------------------------------------------------------------------- #
output "igw_id" {
  description = "ID of the Internet Gateway"
  value       = module.vpc.igw_id
}

output "igw_arn" {
  description = "ARN of the Internet Gateway"
  value       = module.vpc.igw_arn
}

# ------------------------------------------------------------------------------- #
# Subnet Outputs                                                                 #
# ------------------------------------------------------------------------------- #
output "public_subnets" {
  description = "List of IDs of public subnets"
  value       = module.vpc.public_subnets
}

output "public_subnet_arns" {
  description = "List of ARNs of public subnets"
  value       = module.vpc.public_subnet_arns
}

output "public_subnets_cidr_blocks" {
  description = "List of CIDR blocks of public subnets"
  value       = module.vpc.public_subnets_cidr_blocks
}

output "private_subnets" {
  description = "List of IDs of private subnets"
  value       = module.vpc.private_subnets
}

output "private_subnet_arns" {
  description = "List of ARNs of private subnets"
  value       = module.vpc.private_subnet_arns
}

output "private_subnets_cidr_blocks" {
  description = "List of CIDR blocks of private subnets"
  value       = module.vpc.private_subnets_cidr_blocks
}

output "database_subnets" {
  description = "List of IDs of database subnets"
  value       = module.vpc.database_subnets
}

output "database_subnet_arns" {
  description = "List of ARNs of database subnets"
  value       = module.vpc.database_subnet_arns
}

output "database_subnets_cidr_blocks" {
  description = "List of CIDR blocks of database subnets"
  value       = module.vpc.database_subnets_cidr_blocks
}

# ------------------------------------------------------------------------------- #
# NAT Gateway Outputs                                                            #
# ------------------------------------------------------------------------------- #
output "nat_ids" {
  description = "List of IDs of the NAT Gateways"
  value       = module.vpc.nat_ids
}

output "nat_public_ips" {
  description = "List of public Elastic IPs of NAT Gateways"
  value       = module.vpc.nat_public_ips
}

output "natgw_ids" {
  description = "List of NAT Gateway IDs (alias for nat_ids)"
  value       = module.vpc.natgw_ids
}

# ------------------------------------------------------------------------------- #
# Route Table Outputs                                                            #
# ------------------------------------------------------------------------------- #
output "public_route_table_ids" {
  description = "List of IDs of public route tables"
  value       = module.vpc.public_route_table_ids
}

output "private_route_table_ids" {
  description = "List of IDs of private route tables"
  value       = module.vpc.private_route_table_ids
}

output "database_route_table_ids" {
  description = "List of IDs of database route tables"
  value       = module.vpc.database_route_table_ids
}

# ------------------------------------------------------------------------------- #
# Availability Zone Outputs                                                      #
# ------------------------------------------------------------------------------- #
output "azs" {
  description = "List of availability zones used"
  value       = module.vpc.azs
}

# ------------------------------------------------------------------------------- #
# Comprehensive VPC Data Output                                                  #
# ------------------------------------------------------------------------------- #
output "vpc_data" {
  description = "Complete VPC data for use by other modules"
  value       = module.vpc.vpc_data
  sensitive   = false
}

# ------------------------------------------------------------------------------- #
# Summary Outputs for Ansible                                                    #
# ------------------------------------------------------------------------------- #
output "vpc_summary" {
  description = "Summary of VPC infrastructure for Ansible consumption"
  value = {
    vpc_id                = module.vpc.vpc_id
    vpc_cidr             = module.vpc.vpc_cidr_block
    public_subnet_count  = length(module.vpc.public_subnets)
    private_subnet_count = length(module.vpc.private_subnets)
    database_subnet_count = length(module.vpc.database_subnets)
    nat_gateway_count    = length(module.vpc.nat_ids)
    availability_zones   = length(module.vpc.azs)
    
    # Configuration used
    configuration = {
      vpc_cidr             = "{{ vpc_config.vpc_cidr }}"
      create_nat_gateway   = {{ vpc_config.create_nat_gateway | lower }}
      single_nat_gateway   = {{ vpc_config.single_nat_gateway | lower }}
      enable_dns_hostnames = {{ vpc_config.enable_dns_hostnames | lower }}
      enable_dns_support   = {{ vpc_config.enable_dns_support | lower }}
    }
    
    # Metadata
    created_by = "ansible-aws_tfvpc"
    created_at = "{{ ansible_date_time.iso8601 }}"
    account    = "{{ aws_account.id }}"
    region     = "{{ aws_region.name }}"
    environment = "{{ aws_environment.id }}"
  }
}
