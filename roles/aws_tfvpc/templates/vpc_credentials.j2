# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        credentials (Generated by Ansible)                               #
# Version:                                                                        #
#               2024-10-02 AI. Generated AWS credentials file                     #
# Create Date:  {{ ansible_date_time.iso8601 }}                                  #
# Author:       Ansible aws_tfvpc role                                           #
# Description:                                                                    #
#               AWS credentials file for Terraform VPC operations                 #
#               Generated from template: vpc_credentials.j2                      #
#                                                                                 #
# SECURITY NOTE: This file contains sensitive credentials and should be          #
#                handled securely. It will be automatically cleaned up           #
#                after Terraform operations complete.                            #
# ------------------------------------------------------------------------------- #

[{{ aws_common_credentials.init.profile | default('default') }}]
aws_access_key_id = {{ aws_common_credentials.init.access_key_id }}
aws_secret_access_key = {{ aws_common_credentials.init.access_key_secret }}
region = {{ aws_region.name }}

{% if aws_common_credentials.init.session_token is defined %}
aws_session_token = {{ aws_common_credentials.init.session_token }}
{% endif %}

{% if aws_common_credentials.init.role_arn is defined %}
role_arn = {{ aws_common_credentials.init.role_arn }}
{% endif %}

{% if aws_common_credentials.init.source_profile is defined %}
source_profile = {{ aws_common_credentials.init.source_profile }}
{% endif %}

{% if aws_common_credentials.tfstate is defined %}
# Terraform state management credentials
[tfstate]
aws_access_key_id = {{ aws_common_credentials.tfstate.access_key_id }}
aws_secret_access_key = {{ aws_common_credentials.tfstate.access_key_secret }}
region = {{ aws_region.name }}
{% if aws_common_credentials.tfstate.session_token is defined %}
aws_session_token = {{ aws_common_credentials.tfstate.session_token }}
{% endif %}
{% endif %}
