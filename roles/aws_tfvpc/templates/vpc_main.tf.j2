# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.tf (Generated by Ansible)                                   #
# Version:                                                                        #
#               2024-10-02 AI. Generated VPC main configuration                   #
# Create Date:  {{ ansible_date_time.iso8601 }}                                  #
# Author:       Ansible aws_tfvpc role                                           #
# Description:                                                                    #
#               Main Terraform configuration for VPC infrastructure               #
#               Generated from template: vpc_main.tf.j2                          #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# Data Sources                                                                    #
# ------------------------------------------------------------------------------- #
data "aws_partition" "current" {
  provider = aws.{{ aws_region.name }}
}

data "aws_caller_identity" "current" {
  provider = aws.{{ aws_region.name }}
}

data "aws_region" "current" {
  provider = aws.{{ aws_region.name }}
}

# ------------------------------------------------------------------------------- #
# Local Values                                                                   #
# ------------------------------------------------------------------------------- #
locals {
  # Common data for all resources
  common_data = {
    account         = "{{ vpc_common_data.account }}"
    region          = "{{ vpc_common_data.region }}"
    environment     = "{{ vpc_common_data.environment }}"
    prefix          = "{{ vpc_common_data.prefix }}"
    prefix_global   = "{{ vpc_common_data.prefix_global }}"
    service_name    = "{{ vpc_common_data.service_name }}"
    service_id      = "{{ vpc_common_data.service_id }}"
    az_list         = {{ vpc_common_data.az_list | to_json }}
    aws_partition   = data.aws_partition.current.partition
    
    # Tags
    tags = merge(
      {
        Account     = "{{ vpc_common_data.account }}"
        Region      = "{{ vpc_common_data.region }}"
        Environment = "{{ vpc_common_data.environment }}"
        ServiceName = "{{ vpc_common_data.service_name }}"
        ServiceId   = "{{ vpc_common_data.service_id }}"
        ManagedBy   = "terraform"
        CreatedBy   = "ansible-aws_tfvpc"
        CreatedAt   = "{{ ansible_date_time.iso8601 }}"
      },
      {{ vpc_common_data.tags | to_json }}
    )
  }
}

# ------------------------------------------------------------------------------- #
# VPC Module                                                                      #
# ------------------------------------------------------------------------------- #
module "vpc" {
  source = "{{ terraform_module_dir }}/vpc"
  
  # Provider configuration
  providers = {
    aws = aws.{{ aws_region.name }}
  }
  
  # Common data
  common_data = local.common_data
  
  # VPC Configuration
  vpc_cidr             = "{{ vpc_config.vpc_cidr }}"
  enable_dns_hostnames = {{ vpc_config.enable_dns_hostnames | lower }}
  enable_dns_support   = {{ vpc_config.enable_dns_support | lower }}
  
  # Internet Gateway
  create_igw = {{ vpc_config.create_igw | lower }}
  
  # NAT Gateway Configuration
  create_nat_gateway     = {{ vpc_config.create_nat_gateway | lower }}
  single_nat_gateway     = {{ vpc_config.single_nat_gateway | lower }}
  one_nat_gateway_per_az = {{ vpc_config.one_nat_gateway_per_az | lower }}
  
  # Subnet Configuration
  map_public_ip_on_launch = {{ vpc_config.map_public_ip_on_launch | lower }}
  
  # VPC Flow Logs
  enable_flow_log = {{ vpc_config.enable_flow_log | lower }}
{% if vpc_config.flow_log_destination_type is defined %}
  flow_log_destination_type = "{{ vpc_config.flow_log_destination_type }}"
{% endif %}
{% if vpc_config.flow_log_destination_arn is defined %}
  flow_log_destination_arn = "{{ vpc_config.flow_log_destination_arn }}"
{% endif %}
  
  # VPC Endpoints
  enable_s3_endpoint       = {{ vpc_config.enable_s3_endpoint | lower }}
  enable_dynamodb_endpoint = {{ vpc_config.enable_dynamodb_endpoint | lower }}
  
  # Additional Tags
{% if vpc_config.vpc_tags is defined and vpc_config.vpc_tags | length > 0 %}
  vpc_tags = {{ vpc_config.vpc_tags | to_json }}
{% endif %}
{% if vpc_config.public_subnet_tags is defined and vpc_config.public_subnet_tags | length > 0 %}
  public_subnet_tags = {{ vpc_config.public_subnet_tags | to_json }}
{% endif %}
{% if vpc_config.private_subnet_tags is defined and vpc_config.private_subnet_tags | length > 0 %}
  private_subnet_tags = {{ vpc_config.private_subnet_tags | to_json }}
{% endif %}
{% if vpc_config.database_subnet_tags is defined and vpc_config.database_subnet_tags | length > 0 %}
  database_subnet_tags = {{ vpc_config.database_subnet_tags | to_json }}
{% endif %}
}

# ------------------------------------------------------------------------------- #
# Additional Resources (Optional)                                                #
# ------------------------------------------------------------------------------- #

{% if vpc_config.create_vpc_endpoints | default(false) %}
# S3 VPC Endpoint
resource "aws_vpc_endpoint" "s3" {
  count = var.enable_s3_endpoint ? 1 : 0
  
  vpc_id       = module.vpc.vpc_id
  service_name = "com.amazonaws.{{ aws_region.name }}.s3"
  
  tags = merge(
    local.common_data.tags,
    {
      Name = "${local.common_data.prefix}-s3-endpoint"
      Type = "Gateway"
    }
  )
}

# DynamoDB VPC Endpoint
resource "aws_vpc_endpoint" "dynamodb" {
  count = var.enable_dynamodb_endpoint ? 1 : 0
  
  vpc_id       = module.vpc.vpc_id
  service_name = "com.amazonaws.{{ aws_region.name }}.dynamodb"
  
  tags = merge(
    local.common_data.tags,
    {
      Name = "${local.common_data.prefix}-dynamodb-endpoint"
      Type = "Gateway"
    }
  )
}
{% endif %}

{% if vpc_config.create_default_security_group_rules | default(false) %}
# Default Security Group Rules
resource "aws_security_group_rule" "default_ingress_self" {
  type                     = "ingress"
  from_port                = 0
  to_port                  = 65535
  protocol                 = "-1"
  source_security_group_id = module.vpc.default_security_group_id
  security_group_id        = module.vpc.default_security_group_id
  description              = "Allow all traffic from self"
}

resource "aws_security_group_rule" "default_egress_all" {
  type              = "egress"
  from_port         = 0
  to_port           = 65535
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = module.vpc.default_security_group_id
  description       = "Allow all outbound traffic"
}
{% endif %}
