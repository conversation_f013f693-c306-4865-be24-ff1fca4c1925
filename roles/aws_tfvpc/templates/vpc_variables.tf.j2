# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        variables.tf (Generated by Ansible)                              #
# Version:                                                                        #
#               2024-10-02 AI. Generated VPC variables configuration              #
# Create Date:  {{ ansible_date_time.iso8601 }}                                  #
# Author:       Ansible aws_tfvpc role                                           #
# Description:                                                                    #
#               Terraform variables for VPC infrastructure                        #
#               Generated from template: vpc_variables.tf.j2                     #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# ------------------------------------------------------------------------------- #
# VPC Configuration Variables                                                     #
# ------------------------------------------------------------------------------- #
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "{{ vpc_config.vpc_cidr }}"
  
  validation {
    condition = can(cidrhost(var.vpc_cidr, 0))
    error_message = "VPC CIDR must be a valid IPv4 CIDR block."
  }
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = {{ vpc_config.enable_dns_hostnames | lower }}
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = {{ vpc_config.enable_dns_support | lower }}
}

# ------------------------------------------------------------------------------- #
# Internet Gateway Variables                                                      #
# ------------------------------------------------------------------------------- #
variable "create_igw" {
  description = "Create Internet Gateway"
  type        = bool
  default     = {{ vpc_config.create_igw | lower }}
}

# ------------------------------------------------------------------------------- #
# NAT Gateway Variables                                                           #
# ------------------------------------------------------------------------------- #
variable "create_nat_gateway" {
  description = "Create NAT Gateway for private subnets"
  type        = bool
  default     = {{ vpc_config.create_nat_gateway | lower }}
}

variable "single_nat_gateway" {
  description = "Use single NAT gateway for all private subnets"
  type        = bool
  default     = {{ vpc_config.single_nat_gateway | lower }}
}

variable "one_nat_gateway_per_az" {
  description = "Create one NAT gateway per availability zone"
  type        = bool
  default     = {{ vpc_config.one_nat_gateway_per_az | lower }}
}

# ------------------------------------------------------------------------------- #
# Subnet Variables                                                                #
# ------------------------------------------------------------------------------- #
variable "map_public_ip_on_launch" {
  description = "Specify true to indicate that instances launched into the public subnet should be assigned a public IP address"
  type        = bool
  default     = {{ vpc_config.map_public_ip_on_launch | lower }}
}

# ------------------------------------------------------------------------------- #
# VPC Flow Logs Variables                                                         #
# ------------------------------------------------------------------------------- #
variable "enable_flow_log" {
  description = "Enable VPC Flow Logs"
  type        = bool
  default     = {{ vpc_config.enable_flow_log | lower }}
}

{% if vpc_config.flow_log_destination_type is defined %}
variable "flow_log_destination_type" {
  description = "Type of flow log destination"
  type        = string
  default     = "{{ vpc_config.flow_log_destination_type }}"
  
  validation {
    condition = contains(["s3", "cloud-watch-logs"], var.flow_log_destination_type)
    error_message = "Flow log destination type must be either 's3' or 'cloud-watch-logs'."
  }
}
{% endif %}

{% if vpc_config.flow_log_destination_arn is defined %}
variable "flow_log_destination_arn" {
  description = "ARN of the destination for VPC Flow Logs"
  type        = string
  default     = "{{ vpc_config.flow_log_destination_arn }}"
}
{% endif %}

# ------------------------------------------------------------------------------- #
# VPC Endpoints Variables                                                         #
# ------------------------------------------------------------------------------- #
variable "enable_s3_endpoint" {
  description = "Enable S3 VPC Endpoint"
  type        = bool
  default     = {{ vpc_config.enable_s3_endpoint | lower }}
}

variable "enable_dynamodb_endpoint" {
  description = "Enable DynamoDB VPC Endpoint"
  type        = bool
  default     = {{ vpc_config.enable_dynamodb_endpoint | lower }}
}

# ------------------------------------------------------------------------------- #
# Environment and Account Variables                                               #
# ------------------------------------------------------------------------------- #
variable "account" {
  description = "AWS Account ID"
  type        = string
  default     = "{{ aws_account.id }}"
}

variable "region" {
  description = "AWS Region"
  type        = string
  default     = "{{ aws_region.name }}"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "{{ aws_environment.id }}"
}

# ------------------------------------------------------------------------------- #
# Additional Tags Variables                                                       #
# ------------------------------------------------------------------------------- #
{% if vpc_config.vpc_tags is defined and vpc_config.vpc_tags | length > 0 %}
variable "vpc_tags" {
  description = "Additional tags for the VPC"
  type        = map(string)
  default     = {{ vpc_config.vpc_tags | to_json }}
}
{% endif %}

{% if vpc_config.public_subnet_tags is defined and vpc_config.public_subnet_tags | length > 0 %}
variable "public_subnet_tags" {
  description = "Additional tags for public subnets"
  type        = map(string)
  default     = {{ vpc_config.public_subnet_tags | to_json }}
}
{% endif %}

{% if vpc_config.private_subnet_tags is defined and vpc_config.private_subnet_tags | length > 0 %}
variable "private_subnet_tags" {
  description = "Additional tags for private subnets"
  type        = map(string)
  default     = {{ vpc_config.private_subnet_tags | to_json }}
}
{% endif %}

{% if vpc_config.database_subnet_tags is defined and vpc_config.database_subnet_tags | length > 0 %}
variable "database_subnet_tags" {
  description = "Additional tags for database subnets"
  type        = map(string)
  default     = {{ vpc_config.database_subnet_tags | to_json }}
}
{% endif %}
