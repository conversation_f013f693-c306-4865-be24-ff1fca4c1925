# AWS Infrastructure Documentation for Beginners

## Table of Contents
1. [Overview](#overview)
2. [What is Terraform?](#what-is-terraform)
3. [What is Ansible?](#what-is-ansible)
4. [Project Structure](#project-structure)
5. [Terraform Components](#terraform-components)
6. [Ansible Components](#ansible-components)
7. [How They Work Together](#how-they-work-together)
8. [Getting Started](#getting-started)

## Overview

This project uses **Infrastructure as Code (IaC)** to manage AWS cloud resources. It combines two powerful tools:
- **Terraform**: Creates and manages AWS infrastructure
- **Ansible**: Orchestrates Terraform and automates configuration

Think of it like this:
- Terraform is like an architect that designs and builds your cloud infrastructure
- Ansible is like a project manager that coordinates the building process

## What is Terraform?

**Terraform** is a tool that lets you define cloud infrastructure using code instead of clicking through web consoles.

### Key Concepts:
- **Resources**: Things you want to create (like servers, databases, networks)
- **Modules**: Reusable pieces of infrastructure code
- **State**: Terraform remembers what it created so it can update or delete it later
- **Providers**: Plugins that let Terraform talk to cloud services (like AWS)

### Example:
```hcl
# This creates an AWS S3 bucket
resource "aws_s3_bucket" "my_bucket" {
  bucket = "my-unique-bucket-name"
}
```

## What is Ansible?

**Ansible** is an automation tool that can:
- Run commands on multiple servers
- Install software
- Configure systems
- Orchestrate complex workflows

### Key Concepts:
- **Playbooks**: Step-by-step instructions (like recipes)
- **Roles**: Reusable sets of tasks
- **Tasks**: Individual actions to perform
- **Inventory**: List of servers to manage

### Example:
```yaml
# This installs a package
- name: Install nginx
  package:
    name: nginx
    state: present
```

## Project Structure

```
aws-main/
├── terraform/                 # Terraform infrastructure code
│   ├── modules/              # Reusable infrastructure components
│   │   ├── tfstate/         # Manages Terraform state storage
│   │   ├── vpc/             # Virtual Private Cloud setup
│   │   ├── eks_cluster/     # Kubernetes cluster
│   │   ├── eks-node-group/  # Worker nodes for Kubernetes
│   │   └── common/          # Shared configurations
│   └── templates/           # Template files for generating Terraform code
├── roles/                   # Ansible roles
│   ├── aws_common/         # Common AWS configurations
│   ├── aws_tfstate/        # Manages Terraform state
│   ├── aws_tfroot/         # Generates Terraform root files
│   └── aws_tfvpc/          # VPC management
└── extensions/             # Testing and validation tools
```

## Terraform Components

### 1. tfstate Module (`terraform/modules/tfstate/`)

**Purpose**: Sets up secure storage for Terraform's state file.

**What it does**:
- Creates an S3 bucket to store Terraform state
- Sets up DynamoDB table for state locking (prevents conflicts)
- Creates encryption keys for security

**Key Files**:
- `tfstate-main.tf`: Main configuration and variables
- `tfstate-cmk.tf`: Encryption key management
- `tfstate-s3.tf.orig`: S3 bucket configuration (template)

### 2. vpc Module (`terraform/modules/vpc/`)

**Purpose**: Creates the network foundation for your AWS infrastructure.

**What it does**:
- Creates a Virtual Private Cloud (VPC) - your private network in AWS
- Sets up subnets (network segments)
- Configures routing and security

### 3. eks_cluster Module (`terraform/modules/eks_cluster/`)

**Purpose**: Creates a managed Kubernetes cluster on AWS.

**What it does**:
- Creates an EKS (Elastic Kubernetes Service) cluster
- Sets up security groups and access controls
- Configures logging and monitoring
- Manages cluster add-ons

**Key Files**:
- `cluster.tf`: Main cluster configuration
- `iam.tf`: Identity and access management
- `security_group.tf`: Network security rules
- `addons.tf`: Additional cluster features

### 4. eks-node-group Module (`terraform/modules/eks-node-group/`)

**Purpose**: Creates worker nodes for the Kubernetes cluster.

**What it does**:
- Creates EC2 instances that run your applications
- Configures auto-scaling
- Sets up launch templates for consistent node configuration

### 5. common Module (`terraform/modules/common/`)

**Purpose**: Provides shared configurations and data for all modules.

**What it does**:
- Defines common tags for all resources
- Sets up region and account mappings
- Provides shared variables and outputs

**Key Configuration**:
```hcl
# Example from common-main.tf
locals {
  business_area     = "Enterprise Operations"
  service_name      = "OpenShift Container Platform"
  prefix            = "${local.region_id}-${var.environment}"
  
  tags_map = {
    BusinessArea    = local.business_area
    ServiceName     = local.service_name
    Environment     = var.environment
  }
}
```

## Ansible Components

### 1. aws_common Role (`roles/aws_common/`)

**Purpose**: Provides common configurations for all AWS operations.

**What it does**:
- Sets up AWS credentials
- Defines common variables
- Provides shared functionality for other roles

### 2. aws_tfstate Role (`roles/aws_tfstate/`)

**Purpose**: Manages Terraform state infrastructure using Ansible.

**What it does**:
- Orchestrates the creation of state storage
- Manages state bucket lifecycle
- Handles state migration if needed

### 3. aws_tfroot Role (`roles/aws_tfroot/`)

**Purpose**: Dynamically generates Terraform root configurations.

**What it does**:
- Creates Terraform files from templates
- Customizes configurations for different environments
- Manages Terraform execution

**Key Process**:
1. Takes Jinja2 templates from `terraform/templates/`
2. Fills in variables based on environment
3. Creates complete Terraform configurations
4. Runs `terraform apply`

### 4. Templates (`terraform/templates/`)

**Purpose**: Template files that Ansible uses to generate Terraform code.

**Files**:
- `main.j2`: Main Terraform configuration template
- `providers.j2`: AWS provider configuration template
- `variables.j2`: Variable definitions template
- `terraform.j2`: Terraform backend configuration template

## How They Work Together

### The Workflow:

1. **Ansible starts the process**
   - Reads configuration from inventory and variables
   - Determines what infrastructure is needed

2. **Ansible generates Terraform code**
   - Uses Jinja2 templates to create Terraform files
   - Customizes for specific environment (dev, staging, prod)

3. **Ansible runs Terraform**
   - Executes `terraform init` to initialize
   - Runs `terraform plan` to show what will be created
   - Executes `terraform apply` to create infrastructure

4. **Terraform creates AWS resources**
   - Provisions VPC, subnets, security groups
   - Creates EKS cluster and worker nodes
   - Sets up monitoring and logging

### Example Flow:
```
Ansible Playbook → Generate Terraform Code → Run Terraform → Create AWS Infrastructure
```

## Getting Started

### Prerequisites:
1. **AWS Account**: You need access to an AWS account
2. **AWS CLI**: Command-line tool for AWS
3. **Terraform**: Infrastructure as Code tool
4. **Ansible**: Automation tool

### Basic Commands:

#### Terraform Commands:
```bash
# Initialize Terraform (download providers)
terraform init

# See what will be created
terraform plan

# Create the infrastructure
terraform apply

# Destroy the infrastructure
terraform destroy
```

#### Ansible Commands:
```bash
# Run a playbook
ansible-playbook playbook.yml

# Check syntax
ansible-playbook --syntax-check playbook.yml

# Dry run (don't make changes)
ansible-playbook --check playbook.yml
```

### Environment Variables:
```bash
# AWS credentials
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"
```

### Next Steps:
1. Set up your AWS credentials
2. Review the variable files to understand configurations
3. Start with the tfstate module to set up state storage
4. Gradually work through VPC and EKS modules
5. Use Ansible playbooks to orchestrate the entire process

## Important Notes for Beginners:

1. **Always use version control**: Keep your infrastructure code in Git
2. **Test in development first**: Never test directly in production
3. **Understand state**: Terraform state is critical - don't lose it!
4. **Use modules**: Don't repeat code - use the provided modules
5. **Tag everything**: Use consistent tags for cost tracking and organization

This documentation provides a foundation for understanding the infrastructure code. As you become more comfortable, dive deeper into specific modules and configurations.

## Detailed Code Examples

### Terraform State Configuration

The `tfstate-main.tf` file is the entry point for state management:

```hcl
# terraform/modules/tfstate/tfstate-main.tf

# Define required providers
terraform {
  required_providers { aws = {} }
}

# Input variable for common data
variable "common_data" {}

# This variable contains shared information like:
# - AWS account details
# - Region information
# - Common tags
# - Naming conventions
```

**What this means**:
- `terraform` block: Tells Terraform which providers (AWS) are needed
- `variable "common_data"`: Accepts configuration data from parent modules
- This creates a reusable module that can work in any AWS account/region

### EKS Cluster Configuration

The EKS cluster is defined in `cluster.tf`:

```hcl
# terraform/modules/eks_cluster/cluster.tf

resource "aws_eks_cluster" "this" {
  count = var.create == true ? 1 : 0  # Only create if enabled

  name     = var.cluster_name         # Name of your Kubernetes cluster
  role_arn = aws_iam_role.this.arn   # IAM role for cluster permissions
  version  = var.cluster_version      # Kubernetes version (e.g., "1.27")

  # Network configuration
  vpc_config {
    security_group_ids      = [aws_security_group.cluster.id]
    subnet_ids              = var.control_plane_subnet_ids
    endpoint_private_access = true   # Allow private access
    endpoint_public_access  = false  # Block public internet access
  }

  # Security configuration
  encryption_config {
    provider {
      key_arn = aws_kms_key.this.arn  # Encrypt secrets with custom key
    }
    resources = ["secrets"]
  }
}
```

**What this means**:
- Creates a managed Kubernetes cluster in AWS
- Uses private networking (more secure)
- Encrypts sensitive data with custom encryption keys
- Includes proper IAM roles for permissions

### Ansible Role Structure

Ansible roles follow a standard structure. Here's the `aws_tfroot` role:

```yaml
# roles/aws_tfroot/tasks/main.yml

- name: "Debug input variables"
  vars:
    inputs:
      aws_common_credentials: "{{ aws_common_credentials }}"
  ansible.builtin.debug:
    var: inputs
    verbosity: 1

# Execute the main tasks
- name: Execute aws_tfroot_build task
  ansible.builtin.include_tasks: "{{ item }}.yml"
  loop: [aws_tfroot_build, aws_tfroot_apply, aws_tfroot_cleanup]
```

**What this means**:
- Shows input variables for debugging
- Runs three main tasks in sequence:
  1. `aws_tfroot_build`: Generate Terraform files
  2. `aws_tfroot_apply`: Run Terraform to create infrastructure
  3. `aws_tfroot_cleanup`: Clean up temporary files

### Template Generation Process

Ansible uses Jinja2 templates to generate Terraform code:

```yaml
# roles/aws_tfroot/tasks/aws_tfroot_build.yml

- name: Create Terraform Root files from templates
  ansible.builtin.template:
    src: "{{ role_path }}/templates/{{ item }}.j2"
    dest: "{{ aws_tfroot_tempdir.path }}/{{ item }}"
    force: true
    mode: '0644'
  loop: [credentials, main.tf, providers.tf, terraform.tf]
```

**What this means**:
- Takes template files (`.j2` files)
- Fills in variables based on your environment
- Creates actual Terraform files (`.tf` files)
- This allows one template to work for dev, staging, and production

## Common Configuration Patterns

### 1. Resource Naming Convention

All resources follow a consistent naming pattern:

```hcl
# From common-main.tf
locals {
  prefix        = "${local.region_id}-${var.environment}"     # e.g., "use1-dev"
  prefix_global = "${var.account}-${local.region_id}-${var.environment}"  # e.g., "*********-use1-dev"
}

# Used in resource names like:
# S3 bucket: "*********-use1-dev-tfstate"
# EKS cluster: "ocp-use1-dev"
```

### 2. Tagging Strategy

All resources get consistent tags for organization:

```hcl
# From common-main.tf
locals {
  tags_map = {
    BusinessArea    = "Enterprise Operations"
    BusinessOrg     = "Infrastructure and International"
    ServiceOrg      = "Enterprise Container Services"
    ServiceProgram  = "Cloud Service"
    ServiceName     = "OpenShift Container Platform"
    ServiceId       = "ocp"
    RegionId        = local.region_id
    AccountId       = var.account
    Region          = var.region
    Environment     = var.environment
  }
}
```

**Why this matters**:
- Cost tracking: See how much each service costs
- Organization: Find resources quickly
- Compliance: Meet company tagging requirements

### 3. Security Best Practices

The code implements several security best practices:

```hcl
# Private EKS cluster (no public internet access)
vpc_config {
  endpoint_private_access = true
  endpoint_public_access  = false
}

# Encryption at rest
encryption_config {
  provider {
    key_arn = aws_kms_key.this.arn
  }
  resources = ["secrets"]
}

# IAM roles with least privilege
data "aws_iam_policy_document" "default" {
  statement {
    sid       = "Enable IAM User Permissions"
    effect    = "Allow"
    actions   = ["kms:*"]
    resources = ["*"]
    principals {
      type        = "AWS"
      identifiers = [format("arn:%s:iam::%s:root", var.common_data.aws_partition, var.common_data.account)]
    }
  }
}
```

## Troubleshooting Common Issues

### 1. Terraform State Issues

**Problem**: "State file is locked"
**Solution**:
```bash
# Force unlock (use carefully!)
terraform force-unlock LOCK_ID
```

**Problem**: "Resource already exists"
**Solution**:
```bash
# Import existing resource into state
terraform import aws_s3_bucket.example bucket-name
```

### 2. AWS Permission Issues

**Problem**: "Access Denied" errors
**Solution**: Check your AWS credentials and IAM permissions:
```bash
# Verify credentials
aws sts get-caller-identity

# Check if you can list S3 buckets
aws s3 ls
```

### 3. Ansible Template Issues

**Problem**: "Template not found"
**Solution**: Check file paths and ensure templates exist:
```bash
# List template files
ls roles/aws_tfroot/templates/
```

## Best Practices Summary

1. **Version Control**: Always commit infrastructure code to Git
2. **Environment Separation**: Use different AWS accounts for dev/staging/prod
3. **State Management**: Store Terraform state in S3 with DynamoDB locking
4. **Security**: Use private networks and encryption
5. **Monitoring**: Enable CloudWatch logging for EKS clusters
6. **Testing**: Test infrastructure changes in development first
7. **Documentation**: Keep this documentation updated as code changes

## Next Steps for Learning

1. **Start Small**: Begin with the tfstate module
2. **Read the Code**: Study each `.tf` file to understand what it creates
3. **Experiment**: Try changing variables and see what happens (in dev!)
4. **Learn AWS**: Understand VPCs, subnets, and security groups
5. **Practice**: Create your own simple modules
6. **Join Communities**: AWS and Terraform have active communities for help

Remember: Infrastructure as Code is powerful but requires careful planning. Always test changes in a safe environment first!

## Practical Examples

### Example 1: Creating a Development Environment

Here's how you would use this code to create a development environment:

1. **Set up your variables**:
```bash
export AWS_ACCOUNT="************"
export AWS_REGION="us-east-1"
export ENVIRONMENT="dev"
```

2. **Run the Ansible playbook**:
```bash
ansible-playbook -i inventory site.yml \
  -e aws_account=$AWS_ACCOUNT \
  -e aws_region=$AWS_REGION \
  -e environment=$ENVIRONMENT
```

3. **What gets created**:
- S3 bucket: `************-use1-dev-tfstate`
- DynamoDB table: `************-use1-dev-tfstate-lock`
- VPC with private subnets
- EKS cluster: `ocp-use1-dev`
- Worker node groups for the cluster

### Example 2: Understanding Resource Dependencies

Terraform automatically handles dependencies. Here's the order things get created:

```
1. VPC and Subnets (networking foundation)
2. Security Groups (firewall rules)
3. IAM Roles (permissions)
4. KMS Keys (encryption)
5. EKS Cluster (Kubernetes control plane)
6. Node Groups (worker nodes)
7. Add-ons (monitoring, logging)
```

### Example 3: Customizing for Your Organization

To adapt this code for your organization:

1. **Update the common data** in `terraform/modules/common/common-main.tf`:
```hcl
locals {
  business_area     = "Your Business Area"
  business_org      = "Your Organization"
  service_org       = "Your Service Organization"
  service_program   = "Your Program"
  service_name      = "Your Service Name"
  service_id        = "your-service-id"
}
```

2. **Modify the region map** in `terraform/modules/common/common-maps.tf`:
```hcl
local.region_map = {
  "your-account-id" = {
    "us-east-1" = {
      region_id = "use1"
      domain    = "your-domain.com"
      # ... other settings
    }
  }
}
```

3. **Update Ansible variables** in `roles/aws_common/vars/main/`:
```yaml
aws_common_credentials:
  init:
    access_key_id: "{{ lookup('env', 'AWS_ACCESS_KEY_ID') }}"
    access_key_secret: "{{ lookup('env', 'AWS_SECRET_ACCESS_KEY') }}"
    default_region: "{{ lookup('env', 'AWS_DEFAULT_REGION') }}"
```

## Glossary of Terms

**AWS (Amazon Web Services)**: Cloud computing platform that provides servers, storage, networking, and other services.

**EKS (Elastic Kubernetes Service)**: AWS's managed Kubernetes service that runs your containerized applications.

**VPC (Virtual Private Cloud)**: Your private network in AWS, isolated from other customers.

**IAM (Identity and Access Management)**: AWS service that controls who can access what resources.

**S3 (Simple Storage Service)**: AWS object storage service, like a file system in the cloud.

**DynamoDB**: AWS's NoSQL database service, used here for state locking.

**KMS (Key Management Service)**: AWS service for managing encryption keys.

**Terraform State**: File that tracks what infrastructure Terraform has created.

**Jinja2**: Template engine that lets you create dynamic files with variables.

**Ansible Playbook**: YAML file that defines a series of tasks to automate.

**Module**: Reusable piece of Terraform code that creates related resources.

**Provider**: Plugin that lets Terraform communicate with cloud services.

## Additional Resources

### Official Documentation:
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [Ansible Documentation](https://docs.ansible.com/)
- [AWS EKS User Guide](https://docs.aws.amazon.com/eks/latest/userguide/)

### Learning Resources:
- [Terraform Tutorial](https://learn.hashicorp.com/terraform)
- [Ansible Getting Started](https://docs.ansible.com/ansible/latest/user_guide/intro_getting_started.html)
- [AWS Free Tier](https://aws.amazon.com/free/) - Practice with free AWS resources

### Community:
- [Terraform Community](https://discuss.hashicorp.com/c/terraform-core/)
- [Ansible Community](https://forum.ansible.com/)
- [AWS Community](https://aws.amazon.com/developer/community/)

This comprehensive documentation should give you a solid foundation for understanding and working with this Infrastructure as Code project. Start with the basics, experiment safely, and gradually build your expertise!
